/* Global Styles for Aanabi Pharmacy Management System */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    font-size: 14px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #2c3e50, #3498db);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
    display: flex;
    align-items: center;
}

.logo::before {
    content: "🏥";
    margin-right: 0.5rem;
    font-size: 1.8rem;
}

/* Navigation Styles */
.nav {
    background-color: #34495e;
    padding: 0.5rem 0;
    position: fixed;
    top: 70px;
    left: 0;
    right: 0;
    z-index: 999;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 0;
    overflow-x: auto;
    white-space: nowrap;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.nav-menu::-webkit-scrollbar {
    display: none;
}

.nav-item {
    flex-shrink: 0;
}

.nav-link {
    display: block;
    padding: 0.75rem 1rem;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 4px;
    margin-right: 0.25rem;
    font-size: 0.9rem;
}

.nav-link:hover,
.nav-link.active {
    background-color: #3498db;
    color: white;
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    margin-top: 120px;
    min-height: calc(100vh - 180px);
    padding: 1rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.card-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #555;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    text-align: center;
}

.btn:hover {
    background-color: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.btn-primary {
    background-color: #3498db;
}

.btn-success {
    background-color: #27ae60;
}

.btn-success:hover {
    background-color: #219a52;
}

.btn-warning {
    background-color: #f39c12;
    color: #fff;
}

.btn-warning:hover {
    background-color: #e67e22;
}

.btn-danger {
    background-color: #e74c3c;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Table Styles */
.table-container {
    overflow-x: auto;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 0.9rem;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #555;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody tr:nth-child(even) {
    background-color: #fafafa;
}

/* Grid System */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.5rem;
}

.col {
    flex: 1;
    padding: 0 0.5rem;
}

.col-2 {
    width: 16.66%;
}

.col-3 {
    width: 25%;
}

.col-4 {
    width: 33.33%;
}

.col-6 {
    width: 50%;
}

.col-8 {
    width: 66.66%;
}

.col-12 {
    width: 100%;
}

/* Dashboard Specific Styles */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.2s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: #27ae60;
    color: #155724;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #f39c12;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #e74c3c;
    color: #721c24;
}

.alert-info {
    background-color: #cce7ff;
    border-color: #3498db;
    color: #004085;
}

/* Search and Filter */
.search-container {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}

.modal {
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #999;
}

.modal-body {
    padding: 1.5rem;
}

/* Status Indicators */
.status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-active {
    background-color: #d4edda;
    color: #155724;
}

.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-paid {
    background-color: #d4edda;
    color: #155724;
}

.status-overdue {
    background-color: #f8d7da;
    color: #721c24;
}

/* Footer */
.footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 0.5rem;
    }

    .nav-menu {
        flex-direction: column;
        gap: 0.25rem;
    }

    .main-content {
        margin-top: 140px;
        padding: 0.5rem;
    }

    .row {
        flex-direction: column;
    }

    .col-2,
    .col-3,
    .col-4,
    .col-6,
    .col-8 {
        width: 100%;
        margin-bottom: 1rem;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .table-container {
        overflow-x: scroll;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 1.2rem;
    }

    .nav-link {
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .header,
    .nav,
    .btn,
    .modal-overlay {
        display: none !important;
    }

    .main-content {
        margin-top: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.text-muted { color: #7f8c8d; }
.text-success { color: #27ae60; }
.text-warning { color: #f39c12; }
.text-danger { color: #e74c3c; }

.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }

.p-1 { padding: 0.5rem; }
.p-2 { padding: 1rem; }
.p-3 { padding: 1.5rem; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }

.hide { display: none !important; }
.show { display: block !important; }
