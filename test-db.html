<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Test</title>
</head>
<body>
    <h1>Database Test</h1>
    <div id="status">Testing...</div>
    <div id="log"></div>

    <script>
        const log = document.getElementById('log');
        const status = document.getElementById('status');
        
        function addLog(message) {
            console.log(message);
            log.innerHTML += '<div>' + new Date().toLocaleTimeString() + ': ' + message + '</div>';
        }
        
        async function testIndexedDB() {
            try {
                addLog('Testing IndexedDB support...');
                
                if (!window.indexedDB) {
                    throw new Error('IndexedDB not supported');
                }
                
                addLog('IndexedDB is supported');
                
                // Test opening database
                addLog('Opening test database...');
                const request = indexedDB.open('TestDB', 1);
                
                const db = await new Promise((resolve, reject) => {
                    request.onerror = () => reject(request.error);
                    request.onsuccess = () => resolve(request.result);
                    request.onupgradeneeded = (event) => {
                        const db = event.target.result;
                        const store = db.createObjectStore('test', { keyPath: 'id' });
                        addLog('Created test object store');
                    };
                });
                
                addLog('Database opened successfully');
                
                // Test writing data
                addLog('Testing data write...');
                const transaction = db.transaction(['test'], 'readwrite');
                const store = transaction.objectStore('test');
                
                await new Promise((resolve, reject) => {
                    const request = store.add({ id: 1, name: 'Test Item' });
                    request.onsuccess = () => resolve();
                    request.onerror = () => reject(request.error);
                });
                
                addLog('Data written successfully');
                
                // Test reading data
                addLog('Testing data read...');
                const readTransaction = db.transaction(['test'], 'readonly');
                const readStore = readTransaction.objectStore('test');
                
                const data = await new Promise((resolve, reject) => {
                    const request = readStore.get(1);
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });
                
                addLog('Data read successfully: ' + JSON.stringify(data));
                
                db.close();
                
                // Clean up
                await new Promise((resolve, reject) => {
                    const deleteRequest = indexedDB.deleteDatabase('TestDB');
                    deleteRequest.onsuccess = () => resolve();
                    deleteRequest.onerror = () => reject(deleteRequest.error);
                });
                
                addLog('Test database cleaned up');
                status.textContent = 'IndexedDB test passed!';
                status.style.color = 'green';
                
            } catch (error) {
                addLog('ERROR: ' + error.message);
                status.textContent = 'IndexedDB test failed!';
                status.style.color = 'red';
            }
        }
        
        // Run test when page loads
        testIndexedDB();
    </script>
</body>
</html>
