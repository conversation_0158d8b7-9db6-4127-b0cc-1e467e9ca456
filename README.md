# 🏥 Aanabi Pharmacy Management System

A comprehensive web-based pharmacy management system designed for modern pharmacies in Nepal. Built with HTML, CSS, JavaScript, and IndexedDB for offline-first functionality.

## 📋 Table of Contents
- [Features Overview](#features-overview)
- [System Architecture](#system-architecture)
- [Database Schema](#database-schema)
- [Page-wise Functionality](#page-wise-functionality)
- [Installation Guide](#installation-guide)
- [Usage Instructions](#usage-instructions)
- [Technical Specifications](#technical-specifications)
- [Contributing](#contributing)
- [License](#license)

## 🎯 Features Overview

### Core Functionality
- **Point-of-Sale System** - Complete billing and sales management
- **Inventory Management** - Stock tracking with expiry monitoring
- **Customer Relationship Management** - Customer database with loyalty programs
- **Supplier Management** - Vendor tracking and payment management
- **Financial Reporting** - Comprehensive business analytics
- **Smart Alerts** - Proactive notifications for critical events
- **Offline-First Design** - Works without internet connectivity

### Key Highlights
- 🏪 **Multi-store Ready** - Scalable for pharmacy chains
- 📱 **Mobile Responsive** - Works on tablets and smartphones
- 🔒 **Data Security** - Local data storage with encryption support
- 📊 **Business Intelligence** - Real-time analytics and reporting
- 🎨 **Modern UI/UX** - Professional and intuitive interface
- 🌐 **Nepali Market Focus** - VAT calculation, Rs. currency, local compliance

## 🏗️ System Architecture

### Technology Stack
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Database**: IndexedDB (Browser-based NoSQL)
- **Charts**: Chart.js for data visualization
- **Storage**: Client-side persistent storage
- **Architecture**: Single Page Application (SPA) with modular design

### File Structure
```
Aanabi/
├── index.html                 # Main dashboard
├── css/
│   └── style.css             # Complete styling
├── js/
│   ├── database.js           # Database management
│   ├── dashboard.js          # Dashboard functionality
│   ├── medicines.js          # Medicine management
│   ├── sales.js              # Sales operations
│   ├── customers.js          # Customer management
│   ├── suppliers.js          # Supplier management
│   ├── purchase.js           # Purchase management
│   ├── inventory.js          # Inventory tracking
│   ├── reports.js            # Reports and analytics
│   └── settings.js           # System settings
└── pages/
    ├── medicines.html        # Medicine management
    ├── sales.html            # Sales interface
    ├── customers.html        # Customer management
    ├── suppliers.html        # Supplier management
    ├── purchase.html         # Purchase management
    ├── inventory.html        # Inventory tracking
    ├── reports.html          # Analytics dashboard
    └── settings.html         # System configuration
```

## 🗃️ Database Schema

The system uses IndexedDB with the following object stores (tables):

### 1. Medicines Table
Primary store for medicine information and current stock.

| Field | Type | Description |
|-------|------|-------------|
| `medicine_id` | String (PK) | Unique identifier (MED001, MED002...) |
| `medicine_name` | String | Brand/trade name of medicine |
| `generic_name` | String | Generic/chemical name |
| `category` | String | Medicine category (Pain Relief, Antibiotic...) |
| `manufacturer` | String | Manufacturing company |
| `pack_size` | String | Package size (10, 20, 100ml...) |
| `unit_type` | String | Unit type (Tablets, Capsules, Bottle...) |
| `min_stock_level` | Number | Minimum stock alert threshold |
| `current_stock` | Number | Current available quantity |
| `purchase_price` | Number | Cost price per unit |
| `selling_price` | Number | Retail price per unit |
| `margin_percent` | Number | Profit margin percentage |
| `storage_location` | String | Physical storage location (A-1, B-2...) |
| `prescription_required` | String | Yes/No prescription requirement |
| `status` | String | Active/Inactive/Discontinued |

### 2. Inventory Table
Batch-wise inventory tracking with expiry management.

| Field | Type | Description |
|-------|------|-------------|
| `inventory_id` | String (PK) | Unique batch identifier |
| `medicine_id` | String (FK) | Reference to medicines table |
| `batch_number` | String | Manufacturer batch number |
| `manufacturing_date` | Date | Production date |
| `expiry_date` | Date | Expiration date |
| `supplier_name` | String | Supplier information |
| `current_stock` | Number | Batch-specific stock quantity |

### 3. Sales Table
Complete sales transaction records.

| Field | Type | Description |
|-------|------|-------------|
| `sale_id` | String (PK) | Unique sale identifier (SALE001...) |
| `date` | Date | Transaction date |
| `time` | Time | Transaction time |
| `customer_name` | String | Customer name or "Walk-in Customer" |
| `customer_phone` | String | Customer contact number |
| `medicine_id` | String (FK) | Reference to medicines table |
| `medicine_name` | String | Medicine name (denormalized) |
| `quantity` | Number | Quantity sold |
| `unit_price` | Number | Price per unit at time of sale |
| `total_amount` | Number | Subtotal before tax/discount |
| `discount_percent` | Number | Discount percentage applied |
| `discount_amount` | Number | Discount amount in currency |
| `tax_amount` | Number | VAT amount (13% in Nepal) |
| `final_total` | Number | Final amount paid |
| `payment_method` | String | Cash/Card/Digital Payment/Credit |
| `profit_amount` | Number | Profit from this transaction |

### 4. Customers Table
Customer database with medical history and loyalty tracking.

| Field | Type | Description |
|-------|------|-------------|
| `customer_id` | String (PK) | Unique customer identifier (CUST001...) |
| `customer_name` | String | Full customer name |
| `phone_number` | String | Primary contact number |
| `email` | String | Email address |
| `date_of_birth` | Date | Birth date for age-related prescriptions |
| `address` | String | Physical address |
| `medical_conditions` | Text | Known medical conditions |
| `allergies` | Text | Known drug allergies |
| `registration_date` | Date | Customer registration date |
| `last_purchase_date` | Date | Most recent purchase |
| `total_purchase_amount` | Number | Lifetime purchase value |
| `loyalty_points` | Number | Accumulated loyalty points |
| `status` | String | Active/Inactive/VIP |

### 5. Suppliers Table
Vendor management with payment tracking.

| Field | Type | Description |
|-------|------|-------------|
| `supplier_id` | String (PK) | Unique supplier identifier (SUP001...) |
| `supplier_name` | String | Company/supplier name |
| `contact_person` | String | Primary contact person |
| `phone_number` | String | Contact telephone |
| `email` | String | Email address |
| `gst_number` | String | GST/VAT registration number |
| `address` | String | Business address |
| `payment_terms` | String | Payment terms (Net 30, Net 15...) |
| `credit_limit` | Number | Maximum credit allowed |
| `outstanding_amount` | Number | Current outstanding balance |
| `last_purchase_date` | Date | Most recent purchase |
| `total_purchase_amount` | Number | Total business volume |
| `status` | String | Active/Inactive/Suspended |

### 6. Purchases Table
Purchase order and procurement tracking.

| Field | Type | Description |
|-------|------|-------------|
| `purchase_id` | String (PK) | Unique purchase identifier |
| `date` | Date | Purchase order date |
| `supplier_name` | String | Supplier information |
| `medicine_id` | String (FK) | Reference to medicines table |
| `quantity` | Number | Quantity ordered/received |
| `unit_price` | Number | Purchase price per unit |
| `total_amount` | Number | Total purchase value |
| `payment_status` | String | Paid/Pending/Overdue |
| `payment_due_date` | Date | Payment deadline |

### 7. Settings Table
System configuration and preferences.

| Field | Type | Description |
|-------|------|-------------|
| `key` | String (PK) | Setting identifier |
| `value` | String | Setting value |

#### Common Settings:
- `pharmacy_name`: Business name
- `license_number`: Pharmacy license
- `address`: Business address
- `phone`: Contact number
- `email`: Business email
- `gst_number`: Tax registration
- `vat_rate`: VAT percentage (13%)
- `default_margin`: Default profit margin
- `low_stock_alert_days`: Stock alert threshold
- `expiry_alert_days`: Expiry warning period

#### VAT Model Settings:
- `vat_model_enabled`: Enable/disable VAT system
- `vat_registration_number`: VAT registration number
- `vat_registration_date`: VAT registration date
- `vat_calculation_method`: Inclusive or exclusive VAT calculation
- `vat_exempted_categories`: Categories exempt from VAT
- `vat_threshold_amount`: Annual VAT registration threshold
- `vat_return_period`: VAT return filing period
- `vat_invoice_series`: VAT invoice numbering series
- `vat_auto_calculate`: Automatic VAT calculation
- `vat_display_on_receipt`: Show VAT on receipts

#### PAN Model Settings:
- `pan_model_enabled`: Enable/disable PAN system
- `pharmacy_pan_number`: Pharmacy PAN number
- `pan_registration_date`: PAN registration date
- `pan_threshold_amount`: Transaction threshold for PAN requirement
- `pan_mandatory_for_prescription`: PAN requirement for prescriptions
- `pan_mandatory_for_otc`: PAN requirement for OTC medicines
- `pan_required_above_amount`: PAN required above threshold
- `pan_invoice_series`: PAN invoice numbering series
- `pan_customer_verification`: Customer PAN verification level
- `pan_record_keeping_period`: PAN record retention period

## 📱 Page-wise Functionality

### 🏠 Dashboard (index.html)
**Primary landing page with system overview and quick actions.**

#### Features:
- **Real-time Statistics**: Live display of key metrics
  - Total medicines in system
  - Low stock items count
  - Items expiring soon
  - Today's sales revenue
  - Customer and supplier counts

- **Interactive Stat Cards**: Clickable cards that navigate to relevant pages
  - Hover effects and animations
  - Trend indicators showing changes
  - Color-coded status indicators

- **Smart Alert System**: Proactive notifications for critical events
  - Low stock alerts with actionable buttons
  - Expiry warnings with disposal options
  - Payment overdue notifications
  - System health status

- **Recent Sales Transactions**: Latest sales activity
  - Customer information display
  - Transaction details with profit calculation
  - Quick actions (view details, print receipt)
  - Time-ago indicators

- **Low Stock Management**: Critical inventory attention
  - Visual stock level indicators
  - Quick reorder functionality
  - Bulk reorder options
  - Stock level update tools

- **Expiry Management**: Medicine expiry tracking
  - Urgency levels (Critical, Warning, Monitor)
  - Quick discount sale creation
  - Disposal marking functionality
  - Days-left calculations

- **Quick Sale Interface**: Rapid transaction processing
  - Modal-based sale interface
  - Real-time total calculations
  - Instant receipt printing
  - Stock validation

### 💊 Medicines (pages/medicines.html)
**Complete medicine catalog and inventory management.**

#### Features:
- **Medicine Database Management**:
  - Add new medicines with complete information
  - Edit existing medicine details
  - Delete medicines with confirmation
  - Bulk import/export capabilities

- **Inventory Tracking**:
  - Real-time stock level monitoring
  - Minimum stock level alerts
  - Stock value calculations
  - Location-based organization

- **Pricing Management**:
  - Purchase price tracking
  - Selling price management
  - Automatic margin calculation
  - Bulk price updates

- **Search and Filter**:
  - Advanced search by name, category, manufacturer
  - Category-based filtering
  - Stock status filtering
  - Custom date range filtering

- **Reporting Features**:
  - Stock reports generation
  - Low stock alerts
  - Category performance analysis
  - Export to CSV/Excel

### 💳 Sales (pages/sales.html)
**Point-of-sale system with comprehensive billing features.**

#### Features:
- **Dual Sale Modes**:
  - **Regular Sale**: Full featured with discounts, customer lookup
  - **Quick Sale**: Streamlined for fast transactions

- **Customer Integration**:
  - Customer lookup by phone number
  - Automatic customer information filling
  - Loyalty points calculation
  - Purchase history integration

- **Billing Calculations**:
  - Real-time total calculations
  - Discount management (percentage/amount)
  - VAT calculation (13% Nepal standard)
  - Profit margin tracking

- **Payment Processing**:
  - Multiple payment methods (Cash, Card, Digital, Credit)
  - Payment validation
  - Receipt generation
  - Transaction confirmation

- **Inventory Integration**:
  - Real-time stock validation
  - Automatic stock deduction
  - Out-of-stock prevention
  - Stock level warnings

- **Receipt Management**:
  - Professional receipt design
  - Print functionality
  - Digital receipt storage
  - Reprint capabilities

### 👥 Customers (pages/customers.html)
**Customer relationship management with medical history tracking.**

#### Features:
- **Customer Database**:
  - Complete customer profiles
  - Contact information management
  - Address and personal details
  - Registration date tracking

- **Medical Information**:
  - Medical conditions tracking
  - Drug allergy recording
  - Prescription history
  - Age-based recommendations

- **Loyalty Program**:
  - Points accumulation system
  - Purchase value tracking
  - VIP customer designation
  - Reward calculations

- **Purchase Analytics**:
  - Purchase history analysis
  - Spending patterns
  - Frequency analysis
  - Product preferences

- **Communication Tools**:
  - Contact management
  - Reminder systems
  - Birthday tracking
  - Notification preferences

### 🏭 Suppliers (pages/suppliers.html)
**Vendor management with payment and performance tracking.**

#### Features:
- **Supplier Database**:
  - Company information management
  - Contact person details
  - Business registration information
  - Address and location data

- **Financial Management**:
  - Credit limit tracking
  - Outstanding balance monitoring
  - Payment terms management
  - Transaction history

- **Performance Tracking**:
  - Purchase volume analysis
  - Delivery performance
  - Quality assessments
  - Reliability metrics

- **Compliance Management**:
  - GST/VAT number tracking
  - License verification
  - Document management
  - Regulatory compliance

### 🛒 Purchase (pages/purchase.html)
**Procurement management with order tracking.**

#### Features:
- **Purchase Order Management**:
  - Create new purchase orders
  - Edit pending orders
  - Order status tracking
  - Delivery scheduling

- **Supplier Integration**:
  - Supplier selection interface
  - Pricing comparison
  - Terms negotiation
  - Performance history

- **Inventory Updates**:
  - Automatic stock updates on receipt
  - Batch number recording
  - Expiry date tracking
  - Quality control integration

- **Financial Tracking**:
  - Purchase cost management
  - Payment scheduling
  - Outstanding tracking
  - Budget management

### 📦 Inventory (pages/inventory.html)
**Advanced inventory management with batch tracking.**

#### Features:
- **Batch Management**:
  - Batch-wise inventory tracking
  - Manufacturing date recording
  - Expiry date monitoring
  - FIFO/LIFO management

- **Stock Movement**:
  - Movement history tracking
  - Transfer management
  - Adjustment recording
  - Loss/damage tracking

- **Expiry Management**:
  - Expiry date alerts
  - Disposal scheduling
  - Return processing
  - Loss minimization

- **Location Management**:
  - Storage location tracking
  - Zone-based organization
  - Movement optimization
  - Space utilization

### 📊 Reports (pages/reports.html)
**Comprehensive analytics and business intelligence dashboard.**

#### Report Categories:

##### 1. Sales Reports:
- **Daily Sales Summary**: Day-wise transaction analysis
- **Revenue Trends**: Time-series revenue analysis
- **Best Selling Medicines**: Top performers by quantity/revenue
- **Payment Method Analysis**: Payment preference breakdown
- **Customer Purchase Patterns**: Buying behavior analysis
- **Profit Analysis**: Margin and profitability tracking

##### 2. Inventory Reports:
- **Current Stock Report**: Complete inventory status
- **Low Stock Alerts**: Items requiring attention
- **Expiry Reports**: Items expiring in specified periods
- **Stock Movement Reports**: Inventory flow analysis
- **Dead Stock Analysis**: Non-moving inventory identification
- **ABC Analysis**: Inventory categorization by value

##### 3. Financial Reports:
- **Profit & Loss Statement**: Financial performance overview
- **Revenue Reports**: Income analysis and trends
- **Tax Summary**: VAT collection and compliance
- **Outstanding Payments**: Pending receivables/payables
- **Cash Flow Analysis**: Money movement tracking
- **Budget vs Actual**: Performance against targets

##### 4. Analytics Reports:
- **Customer Analytics**: Customer segmentation and behavior
- **Supplier Performance**: Vendor analysis and ratings
- **Seasonal Trends**: Time-based pattern analysis
- **Growth Analytics**: Business growth metrics
- **Market Analysis**: Category and product performance
- **Operational Efficiency**: Process optimization insights

#### Interactive Features:
- **Date Range Filtering**: Custom time period selection
- **Export Capabilities**: CSV, Excel, PDF export options
- **Interactive Charts**: Drill-down and zoom functionality
- **Real-time Updates**: Live data refresh
- **Print Optimization**: Report printing layouts
- **Mobile Responsive**: Full functionality on mobile devices

### ⚙️ Settings (pages/settings.html)
**System configuration and customization.**

#### Features:
- **Business Information**: Pharmacy details and branding
- **Tax Configuration**: VAT rates and tax settings
- **Alert Thresholds**: Stock and expiry warning levels
- **User Preferences**: Interface customization
- **Backup/Restore**: Data management tools
- **System Maintenance**: Performance optimization

## 🚀 Installation Guide

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Local web server (optional for file:// protocol issues)

### Installation Steps

1. **Download/Clone the Repository**:
   ```bash
   git clone [repository-url]
   cd Aanabi
   ```

2. **Local Server Setup** (Recommended):
   ```bash
   # Using Python
   python -m http.server 8000
   
   # Using Node.js
   npx http-server
   
   # Using PHP
   php -S localhost:8000
   ```

3. **Access the Application**:
   - Open browser and navigate to `http://localhost:8000`
   - Or open `index.html` directly in browser

4. **First-time Setup**:
   - The system will automatically initialize the database
   - Sample data will be loaded for demonstration
   - Access the Settings page to configure your pharmacy details

### File Structure Verification
Ensure all files are present:
- ✅ index.html (main dashboard)
- ✅ css/style.css (styling)
- ✅ js/*.js (all JavaScript modules)
- ✅ pages/*.html (all page templates)

## 📖 Usage Instructions

### Initial Setup
1. **Configure Pharmacy Information**:
   - Navigate to Settings page
   - Enter pharmacy name, license, address
   - Set tax rates and business preferences

2. **Add Initial Inventory**:
   - Go to Medicines page
   - Add your medicine catalog
   - Set stock levels and pricing

3. **Setup Suppliers**:
   - Add supplier information
   - Configure payment terms
   - Set credit limits

### Daily Operations

#### Making a Sale:
1. Access Sales page or use Quick Sale on Dashboard
2. Select medicine from dropdown
3. Enter quantity (validates against stock)
4. Apply discounts if applicable
5. Select payment method
6. Complete transaction and print receipt

#### Managing Inventory:
1. Monitor Dashboard alerts for low stock
2. Use Inventory page for batch tracking
3. Set up reorder points for automatic alerts
4. Track expiry dates and manage disposals

#### Customer Management:
1. Register new customers with contact details
2. Record medical conditions and allergies
3. Track purchase history and loyalty points
4. Manage VIP customer benefits

#### Generating Reports:
1. Access Reports page
2. Select appropriate report category
3. Set date range for analysis
4. Export data for external analysis
5. Use insights for business decisions

### Best Practices
- **Regular Backups**: Export data regularly
- **Stock Monitoring**: Check alerts daily
- **Customer Data**: Keep medical information updated
- **Financial Tracking**: Review reports weekly
- **System Maintenance**: Clear old data periodically

## 🔧 Technical Specifications

### Browser Compatibility
- Chrome 80+ ✅
- Firefox 75+ ✅
- Safari 13+ ✅
- Edge 80+ ✅

### Performance Specifications
- **Database Size**: Supports 10,000+ records per table
- **Concurrent Users**: Single-user design (local storage)
- **Response Time**: <100ms for most operations
- **Storage Capacity**: Limited by browser storage quota (~1GB+)

### Security Features
- **Data Isolation**: Browser sandbox security
- **Local Storage**: No external data transmission
- **Input Validation**: XSS and injection prevention
- **Access Control**: Session-based access (extensible)

### API Integration Ready
The system is designed for easy API integration:
- Modular database layer
- RESTful operation patterns
- JSON data structures
- Event-driven architecture

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch
3. Follow coding standards
4. Test thoroughly
5. Submit pull request

### Coding Standards
- **JavaScript**: ES6+ features, modular design
- **CSS**: BEM methodology, responsive design
- **HTML**: Semantic markup, accessibility compliance
- **Documentation**: JSDoc for functions, inline comments

### Feature Requests
- Create GitHub issues for new features
- Provide detailed use cases
- Include mockups for UI features
- Consider backward compatibility

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

### Commercial Use
- ✅ Commercial usage allowed
- ✅ Modification and distribution permitted
- ✅ Private use encouraged
- ⚠️ No warranty or liability

## 🆘 Support

### Documentation
- README.md (this file)
- Inline code comments
- JSDoc function documentation
- User manual in docs/ folder

### Community
- GitHub Issues for bug reports
- Discussions for feature requests
- Wiki for extended documentation
- Community forum (if available)

### Professional Support
For professional support, customization, or enterprise features, please contact the development team.

---

**Built with ❤️ for the Nepali Pharmacy Community**

*Last Updated: June 2025*
*Version: 1.0.0*