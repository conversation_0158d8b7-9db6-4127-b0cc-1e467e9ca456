// Reports and analytics functionality

class ReportsManager {
    constructor() {
        this.sales = [];
        this.medicines = [];
        this.customers = [];
        this.suppliers = [];
        this.purchases = [];
        this.inventory = [];
        this.currentPeriod = 'month';
        this.dateRange = { from: null, to: null };
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.updateDateTime();
        this.setDefaultDateRange();
        this.updateReports();
        setInterval(() => this.updateDateTime(), 1000);
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadData() {
        try {
            // Wait for database to be ready
            if (!dbManager.db) {
                setTimeout(() => this.loadData(), 1000);
                return;
            }

            this.sales = await dbManager.getAll('sales');
            this.medicines = await dbManager.getAll('medicines');
            this.customers = await dbManager.getAll('customers');
            this.suppliers = await dbManager.getAll('suppliers');
            this.purchases = await dbManager.getAll('purchases');
            this.inventory = await dbManager.getAll('inventory');
        } catch (error) {
            console.error('Error loading reports data:', error);
        }
    }

    setupEventListeners() {
        const periodSelect = document.getElementById('report-period');
        if (periodSelect) {
            periodSelect.addEventListener('change', (e) => {
                this.currentPeriod = e.target.value;
                this.toggleCustomDateRange();
                if (this.currentPeriod !== 'custom') {
                    this.setDefaultDateRange();
                    this.updateReports();
                }
            });
        }
    }

    toggleCustomDateRange() {
        const customRange = document.getElementById('custom-date-range');
        if (customRange) {
            customRange.style.display = this.currentPeriod === 'custom' ? 'flex' : 'none';
        }
    }

    setDefaultDateRange() {
        const now = new Date();
        let fromDate, toDate;

        switch (this.currentPeriod) {
            case 'today':
                fromDate = new Date(now);
                toDate = new Date(now);
                break;
            case 'week':
                fromDate = new Date(now);
                fromDate.setDate(now.getDate() - 7);
                toDate = new Date(now);
                break;
            case 'month':
                fromDate = new Date(now.getFullYear(), now.getMonth(), 1);
                toDate = new Date(now);
                break;
            case 'quarter':
                const quarter = Math.floor(now.getMonth() / 3);
                fromDate = new Date(now.getFullYear(), quarter * 3, 1);
                toDate = new Date(now);
                break;
            case 'year':
                fromDate = new Date(now.getFullYear(), 0, 1);
                toDate = new Date(now);
                break;
            default:
                fromDate = new Date(now.getFullYear(), now.getMonth(), 1);
                toDate = new Date(now);
        }

        this.dateRange.from = fromDate;
        this.dateRange.to = toDate;

        // Update custom date inputs
        document.getElementById('from-date').value = fromDate.toISOString().split('T')[0];
        document.getElementById('to-date').value = toDate.toISOString().split('T')[0];
    }

    getFilteredSales() {
        return this.sales.filter(sale => {
            const saleDate = new Date(sale.date);
            return saleDate >= this.dateRange.from && saleDate <= this.dateRange.to;
        });
    }

    updateReports() {
        if (this.currentPeriod === 'custom') {
            this.dateRange.from = new Date(document.getElementById('from-date').value);
            this.dateRange.to = new Date(document.getElementById('to-date').value);
        }

        this.updateSummaryStats();
        this.updateSalesReport();
        this.updateInventoryReport();
        this.updateFinancialReport();
        this.updateCustomerReport();
        this.updateSupplierReport();
    }

    updateSummaryStats() {
        const filteredSales = this.getFilteredSales();
        const totalRevenue = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.final_total) || 0), 0);
        const totalProfit = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.profit_amount) || 0), 0);
        const totalTransactions = filteredSales.length;
        const profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue * 100).toFixed(2) : 0;

        document.getElementById('period-revenue').textContent = `Rs. ${totalRevenue.toLocaleString()}`;
        document.getElementById('period-profit').textContent = `Rs. ${totalProfit.toLocaleString()}`;
        document.getElementById('period-transactions').textContent = totalTransactions;
        document.getElementById('profit-margin').textContent = `${profitMargin}%`;
    }

    updateSalesReport() {
        this.updateTopMedicines();
        this.updateCategorySales();
        this.updateDailySales();
    }

    updateTopMedicines() {
        const filteredSales = this.getFilteredSales();
        const medicineStats = {};

        filteredSales.forEach(sale => {
            const medicineId = sale.medicine_id;
            if (!medicineStats[medicineId]) {
                medicineStats[medicineId] = {
                    name: sale.medicine_name,
                    quantity: 0,
                    revenue: 0,
                    profit: 0
                };
            }
            medicineStats[medicineId].quantity += sale.quantity;
            medicineStats[medicineId].revenue += parseFloat(sale.final_total) || 0;
            medicineStats[medicineId].profit += parseFloat(sale.profit_amount) || 0;
        });

        const topMedicines = Object.values(medicineStats)
            .sort((a, b) => b.revenue - a.revenue)
            .slice(0, 10);

        const tbody = document.getElementById('top-medicines-tbody');
        tbody.innerHTML = '';

        topMedicines.forEach(med => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${med.name}</td>
                <td>${med.quantity}</td>
                <td>Rs. ${med.revenue.toFixed(2)}</td>
                <td>Rs. ${med.profit.toFixed(2)}</td>
            `;
            tbody.appendChild(row);
        });

        if (topMedicines.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">No sales data found for this period</td></tr>';
        }
    }

    updateCategorySales() {
        const filteredSales = this.getFilteredSales();
        const categoryStats = {};
        let totalRevenue = 0;

        filteredSales.forEach(sale => {
            const medicine = this.medicines.find(m => m.medicine_id === sale.medicine_id);
            const category = medicine ? medicine.category : 'Unknown';
            
            if (!categoryStats[category]) {
                categoryStats[category] = {
                    quantity: 0,
                    revenue: 0
                };
            }
            categoryStats[category].quantity += sale.quantity;
            categoryStats[category].revenue += parseFloat(sale.final_total) || 0;
            totalRevenue += parseFloat(sale.final_total) || 0;
        });

        const categoryData = Object.entries(categoryStats)
            .map(([category, stats]) => ({
                category,
                ...stats,
                percentage: totalRevenue > 0 ? (stats.revenue / totalRevenue * 100).toFixed(2) : 0
            }))
            .sort((a, b) => b.revenue - a.revenue);

        const tbody = document.getElementById('category-sales-tbody');
        tbody.innerHTML = '';

        categoryData.forEach(cat => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${cat.category}</td>
                <td>${cat.quantity}</td>
                <td>Rs. ${cat.revenue.toFixed(2)}</td>
                <td>${cat.percentage}%</td>
            `;
            tbody.appendChild(row);
        });

        if (categoryData.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">No category data found</td></tr>';
        }
    }

    updateDailySales() {
        const filteredSales = this.getFilteredSales();
        const dailyStats = {};

        filteredSales.forEach(sale => {
            const date = sale.date;
            if (!dailyStats[date]) {
                dailyStats[date] = {
                    transactions: 0,
                    revenue: 0,
                    profit: 0
                };
            }
            dailyStats[date].transactions += 1;
            dailyStats[date].revenue += parseFloat(sale.final_total) || 0;
            dailyStats[date].profit += parseFloat(sale.profit_amount) || 0;
        });

        const dailyData = Object.entries(dailyStats)
            .map(([date, stats]) => ({
                date,
                ...stats,
                avgTransaction: stats.transactions > 0 ? stats.revenue / stats.transactions : 0
            }))
            .sort((a, b) => new Date(b.date) - new Date(a.date))
            .slice(0, 15);

        const tbody = document.getElementById('daily-sales-tbody');
        tbody.innerHTML = '';

        dailyData.forEach(day => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(day.date).toLocaleDateString()}</td>
                <td>${day.transactions}</td>
                <td>Rs. ${day.revenue.toFixed(2)}</td>
                <td>Rs. ${day.profit.toFixed(2)}</td>
                <td>Rs. ${day.avgTransaction.toFixed(2)}</td>
            `;
            tbody.appendChild(row);
        });

        if (dailyData.length === 0) {
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">No daily sales data found</td></tr>';
        }
    }

    updateInventoryReport() {
        const totalItems = this.inventory.length;
        const totalValue = this.inventory.reduce((sum, item) => sum + (item.current_stock * item.unit_cost), 0);
        const lowStockItems = this.medicines.filter(m => m.current_stock <= m.min_stock_level);

        document.getElementById('total-inventory-items').textContent = totalItems;
        document.getElementById('total-inventory-value').textContent = `Rs. ${totalValue.toLocaleString()}`;
        document.getElementById('critical-stock-items').textContent = lowStockItems.length;

        this.updateLowStockReport();
        this.updateExpiringReport();
    }

    updateLowStockReport() {
        const lowStockItems = this.medicines.filter(m => m.current_stock <= m.min_stock_level);
        const tbody = document.getElementById('low-stock-report-tbody');
        tbody.innerHTML = '';

        lowStockItems.slice(0, 10).forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.medicine_name}</td>
                <td><span class="text-danger">${item.current_stock}</span></td>
                <td>${item.min_stock_level}</td>
                <td><span class="status status-overdue">Critical</span></td>
            `;
            tbody.appendChild(row);
        });

        if (lowStockItems.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-success">All items are well stocked!</td></tr>';
        }
    }

    updateExpiringReport() {
        const today = new Date();
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(today.getDate() + 30);

        const expiringItems = this.inventory.filter(item => {
            const expiryDate = new Date(item.expiry_date);
            return expiryDate <= thirtyDaysFromNow && expiryDate > today;
        });

        const tbody = document.getElementById('expiring-report-tbody');
        tbody.innerHTML = '';

        expiringItems.slice(0, 10).forEach(item => {
            const expiryDate = new Date(item.expiry_date);
            const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.medicine_name}</td>
                <td>${item.batch_number}</td>
                <td>${expiryDate.toLocaleDateString()}</td>
                <td><span class="text-warning">${daysLeft} days</span></td>
            `;
            tbody.appendChild(row);
        });

        if (expiringItems.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-success">No items expiring soon!</td></tr>';
        }
    }

    updateFinancialReport() {
        const filteredSales = this.getFilteredSales();
        
        const totalSales = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.total_amount) || 0), 0);
        const totalDiscount = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.discount_amount) || 0), 0);
        const totalTax = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.tax_amount) || 0), 0);
        const netRevenue = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.final_total) || 0), 0);
        const totalProfit = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.profit_amount) || 0), 0);
        const cogs = netRevenue - totalProfit;
        const operatingExpenses = 5000; // Fixed operating expenses
        const totalExpenses = cogs + operatingExpenses;
        const netProfit = netRevenue - totalExpenses;

        document.getElementById('pl-total-sales').textContent = `Rs. ${totalSales.toLocaleString()}`;
        document.getElementById('pl-total-discount').textContent = `Rs. ${totalDiscount.toLocaleString()}`;
        document.getElementById('pl-total-tax').textContent = `Rs. ${totalTax.toLocaleString()}`;
        document.getElementById('pl-net-revenue').textContent = `Rs. ${netRevenue.toLocaleString()}`;
        document.getElementById('pl-cogs').textContent = `Rs. ${cogs.toLocaleString()}`;
        document.getElementById('pl-operating-expenses').textContent = `Rs. ${operatingExpenses.toLocaleString()}`;
        document.getElementById('pl-total-expenses').textContent = `Rs. ${totalExpenses.toLocaleString()}`;
        document.getElementById('pl-net-profit').textContent = `Rs. ${netProfit.toLocaleString()}`;

        this.updatePaymentMethodAnalysis();
    }

    updatePaymentMethodAnalysis() {
        const filteredSales = this.getFilteredSales();
        const paymentStats = {};
        let totalAmount = 0;

        filteredSales.forEach(sale => {
            const method = sale.payment_method;
            if (!paymentStats[method]) {
                paymentStats[method] = {
                    transactions: 0,
                    amount: 0
                };
            }
            paymentStats[method].transactions += 1;
            paymentStats[method].amount += parseFloat(sale.final_total) || 0;
            totalAmount += parseFloat(sale.final_total) || 0;
        });

        const paymentData = Object.entries(paymentStats)
            .map(([method, stats]) => ({
                method,
                ...stats,
                percentage: totalAmount > 0 ? (stats.amount / totalAmount * 100).toFixed(2) : 0
            }))
            .sort((a, b) => b.amount - a.amount);

        const tbody = document.getElementById('payment-method-tbody');
        tbody.innerHTML = '';

        paymentData.forEach(payment => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${payment.method}</td>
                <td>${payment.transactions}</td>
                <td>Rs. ${payment.amount.toFixed(2)}</td>
                <td>${payment.percentage}%</td>
            `;
            tbody.appendChild(row);
        });

        if (paymentData.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">No payment data found</td></tr>';
        }
    }

    updateCustomerReport() {
        const filteredSales = this.getFilteredSales();
        const customerStats = {};

        filteredSales.forEach(sale => {
            const customerPhone = sale.customer_phone;
            const customerName = sale.customer_name;
            
            if (customerPhone && customerPhone !== '') {
                if (!customerStats[customerPhone]) {
                    customerStats[customerPhone] = {
                        name: customerName,
                        phone: customerPhone,
                        totalPurchases: 0,
                        visits: 0
                    };
                }
                customerStats[customerPhone].totalPurchases += parseFloat(sale.final_total) || 0;
                customerStats[customerPhone].visits += 1;
            }
        });

        const topCustomers = Object.values(customerStats)
            .sort((a, b) => b.totalPurchases - a.totalPurchases)
            .slice(0, 10);

        const tbody = document.getElementById('top-customers-tbody');
        tbody.innerHTML = '';

        topCustomers.forEach(customer => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${customer.name}</td>
                <td>${customer.phone}</td>
                <td>Rs. ${customer.totalPurchases.toFixed(2)}</td>
                <td>${customer.visits}</td>
            `;
            tbody.appendChild(row);
        });

        if (topCustomers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">No customer data found</td></tr>';
        }

        // Customer summary stats
        const totalCustomers = this.customers.length;
        const periodCustomers = this.customers.filter(c => {
            const regDate = new Date(c.registration_date);
            return regDate >= this.dateRange.from && regDate <= this.dateRange.to;
        }).length;
        const avgCustomerValue = totalCustomers > 0 ? 
            this.customers.reduce((sum, c) => sum + (c.total_purchase_amount || 0), 0) / totalCustomers : 0;
        const loyaltyCustomers = this.customers.filter(c => c.status === 'VIP').length;

        document.getElementById('total-registered-customers').textContent = totalCustomers;
        document.getElementById('new-customers-period').textContent = periodCustomers;
        document.getElementById('avg-customer-value').textContent = `Rs. ${avgCustomerValue.toFixed(0)}`;
        document.getElementById('loyalty-customers').textContent = loyaltyCustomers;
    }

    updateSupplierReport() {
        const supplierStats = {};

        this.purchases.forEach(purchase => {
            const supplier = purchase.supplier_name;
            if (!supplierStats[supplier]) {
                supplierStats[supplier] = {
                    orders: 0,
                    totalAmount: 0,
                    outstanding: 0,
                    lastOrder: null
                };
            }
            supplierStats[supplier].orders += 1;
            supplierStats[supplier].totalAmount += parseFloat(purchase.total_amount) || 0;
            if (purchase.payment_status === 'Pending') {
                supplierStats[supplier].outstanding += parseFloat(purchase.total_amount) || 0;
            }
            
            const orderDate = new Date(purchase.date);
            if (!supplierStats[supplier].lastOrder || orderDate > supplierStats[supplier].lastOrder) {
                supplierStats[supplier].lastOrder = orderDate;
            }
        });

        const supplierData = Object.entries(supplierStats)
            .map(([name, stats]) => ({ name, ...stats }))
            .sort((a, b) => b.totalAmount - a.totalAmount);

        const tbody = document.getElementById('supplier-performance-tbody');
        tbody.innerHTML = '';

        supplierData.forEach(supplier => {
            const supplierRecord = this.suppliers.find(s => s.supplier_name === supplier.name);
            const status = supplierRecord ? supplierRecord.status : 'Unknown';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${supplier.name}</td>
                <td>${supplier.orders}</td>
                <td>Rs. ${supplier.totalAmount.toFixed(2)}</td>
                <td>Rs. ${supplier.outstanding.toFixed(2)}</td>
                <td>${supplier.lastOrder ? supplier.lastOrder.toLocaleDateString() : 'Never'}</td>
                <td><span class="status status-${status.toLowerCase()}">${status}</span></td>
            `;
            tbody.appendChild(row);
        });

        if (supplierData.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No supplier data found</td></tr>';
        }
    }
}

// Global functions
function showReport(reportType) {
    // Update active button
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    // Hide all report sections
    document.querySelectorAll('.report-section').forEach(section => {
        section.style.display = 'none';
    });
    
    // Show selected report
    const reportSection = document.getElementById(`${reportType}-report`);
    if (reportSection) {
        reportSection.style.display = 'block';
    }
}

function updateReports() {
    reportsManager.updateReports();
}

function exportReport(reportType) {
    try {
        let csvContent = '';
        let filename = '';
        
        switch (reportType) {
            case 'sales':
                csvContent = generateSalesCSV();
                filename = 'sales_report';
                break;
            case 'inventory':
                csvContent = generateInventoryCSV();
                filename = 'inventory_report';
                break;
            case 'financial':
                csvContent = generateFinancialCSV();
                filename = 'financial_report';
                break;
            case 'complete':
                csvContent = generateCompleteCSV();
                filename = 'complete_report';
                break;
            default:
                alert('Report type not supported');
                return;
        }
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${filename}_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        alert('Report exported successfully!');
    } catch (error) {
        console.error('Error exporting report:', error);
        alert('Error exporting report. Please try again.');
    }
}

function generateSalesCSV() {
    const filteredSales = reportsManager.getFilteredSales();
    const headers = ['Sale ID', 'Date', 'Customer', 'Medicine', 'Quantity', 'Unit Price', 'Total Amount', 'Profit', 'Payment Method'];
    
    let csvContent = headers.join(',') + '\n';
    
    filteredSales.forEach(sale => {
        const row = [
            sale.sale_id,
            sale.date,
            `"${sale.customer_name || 'Walk-in'}"`,
            `"${sale.medicine_name}"`,
            sale.quantity,
            sale.unit_price,
            sale.final_total,
            sale.profit_amount,
            sale.payment_method
        ];
        csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
}

function generateInventoryCSV() {
    const headers = ['Medicine', 'Current Stock', 'Min Level', 'Status', 'Value'];
    
    let csvContent = headers.join(',') + '\n';
    
    reportsManager.medicines.forEach(medicine => {
        const status = medicine.current_stock <= medicine.min_stock_level ? 'Low Stock' : 'Good';
        const value = medicine.current_stock * medicine.purchase_price;
        
        const row = [
            `"${medicine.medicine_name}"`,
            medicine.current_stock,
            medicine.min_stock_level,
            status,
            value.toFixed(2)
        ];
        csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
}

function generateFinancialCSV() {
    const filteredSales = reportsManager.getFilteredSales();
    const totalRevenue = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.final_total) || 0), 0);
    const totalProfit = filteredSales.reduce((sum, sale) => sum + (parseFloat(sale.profit_amount) || 0), 0);
    
    const headers = ['Metric', 'Amount'];
    let csvContent = headers.join(',') + '\n';
    
    const financialData = [
        ['Total Revenue', totalRevenue.toFixed(2)],
        ['Total Profit', totalProfit.toFixed(2)],
        ['Profit Margin %', totalRevenue > 0 ? (totalProfit / totalRevenue * 100).toFixed(2) : 0],
        ['Total Transactions', filteredSales.length]
    ];
    
    financialData.forEach(row => {
        csvContent += row.join(',') + '\n';
    });
    
    return csvContent;
}

function generateCompleteCSV() {
    return 'Complete Report\n\n' + 
           'SALES REPORT\n' + generateSalesCSV() + 
           '\n\nINVENTORY REPORT\n' + generateInventoryCSV() + 
           '\n\nFINANCIAL REPORT\n' + generateFinancialCSV();
}

// Initialize reports manager when page loads
let reportsManager;
document.addEventListener('DOMContentLoaded', () => {
    reportsManager = new ReportsManager();
});