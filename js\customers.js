// Customer management functionality

class CustomerManager {
    constructor() {
        this.customers = [];
        this.sales = [];
        this.filteredCustomers = [];
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadData() {
        try {
            // Wait for database to be ready
            if (!dbManager.db) {
                setTimeout(() => this.loadData(), 1000);
                return;
            }

            this.customers = await dbManager.getAll('customers');
            this.sales = await dbManager.getAll('sales');
            
            this.updateCustomerStats();
            this.filteredCustomers = [...this.customers];
            this.renderCustomers();
            this.updateStatistics();
        } catch (error) {
            console.error('Error loading customer data:', error);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('customer-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.filterCustomers(e.target.value));
        }

        // Form submission
        const form = document.getElementById('customer-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    updateCustomerStats() {
        // Update customer purchase amounts and loyalty points from sales data
        this.customers.forEach(customer => {
            const customerSales = this.sales.filter(sale => sale.customer_phone === customer.phone_number);
            const totalPurchases = customerSales.reduce((sum, sale) => sum + (parseFloat(sale.final_total) || 0), 0);
            const lastPurchase = customerSales.length > 0 ? 
                Math.max(...customerSales.map(sale => new Date(sale.date).getTime())) : null;

            customer.total_purchase_amount = totalPurchases;
            customer.loyalty_points = Math.floor(totalPurchases / 20); // 1 point per Rs. 20
            customer.last_purchase_date = lastPurchase ? new Date(lastPurchase).toISOString().split('T')[0] : null;

            // Update VIP status based on purchase amount
            if (totalPurchases > 50000 && customer.status !== 'VIP') {
                customer.status = 'VIP';
            }
        });
    }

    filterCustomers(searchTerm) {
        const term = searchTerm.toLowerCase();
        this.filteredCustomers = this.customers.filter(customer => 
            customer.customer_name.toLowerCase().includes(term) ||
            customer.phone_number.includes(term) ||
            (customer.email && customer.email.toLowerCase().includes(term)) ||
            customer.customer_id.toLowerCase().includes(term) ||
            (customer.address && customer.address.toLowerCase().includes(term))
        );
        this.renderCustomers();
    }

    renderCustomers() {
        const tbody = document.getElementById('customers-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.filteredCustomers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="13" class="text-center text-muted">No customers found</td></tr>';
            return;
        }

        this.filteredCustomers.forEach(customer => {
            const row = document.createElement('tr');
            
            // Highlight VIP customers
            if (customer.status === 'VIP') {
                row.style.backgroundColor = '#fff8e1';
            }

            row.innerHTML = `
                <td>${customer.customer_id}</td>
                <td>${customer.customer_name}</td>
                <td>${customer.phone_number}</td>
                <td>${customer.email || '-'}</td>
                <td>${customer.address || '-'}</td>
                <td>${customer.date_of_birth ? new Date(customer.date_of_birth).toLocaleDateString() : '-'}</td>
                <td>${customer.medical_conditions || '-'}</td>
                <td>${customer.allergies || '-'}</td>
                <td>Rs. ${(customer.total_purchase_amount || 0).toLocaleString()}</td>
                <td>${customer.loyalty_points || 0}</td>
                <td>${customer.last_purchase_date ? new Date(customer.last_purchase_date).toLocaleDateString() : 'Never'}</td>
                <td><span class="status status-${customer.status.toLowerCase()}">${customer.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="viewCustomerDetails('${customer.customer_id}')">View</button>
                    <button class="btn btn-sm btn-warning" onclick="editCustomer('${customer.customer_id}')">Edit</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteCustomer('${customer.customer_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateStatistics() {
        const totalCustomers = this.customers.length;
        const vipCustomers = this.customers.filter(c => c.status === 'VIP').length;
        const totalRevenue = this.customers.reduce((sum, c) => sum + (c.total_purchase_amount || 0), 0);
        const totalLoyaltyPoints = this.customers.reduce((sum, c) => sum + (c.loyalty_points || 0), 0);

        document.getElementById('total-customers').textContent = totalCustomers;
        document.getElementById('vip-customers').textContent = vipCustomers;
        document.getElementById('total-revenue').textContent = `Rs. ${totalRevenue.toLocaleString()}`;
        document.getElementById('loyalty-points').textContent = totalLoyaltyPoints;
    }

    async handleFormSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const customerData = Object.fromEntries(formData.entries());
        
        // Convert numeric fields
        customerData.total_purchase_amount = parseFloat(customerData.total_purchase_amount) || 0;
        customerData.loyalty_points = parseInt(customerData.loyalty_points) || 0;

        // Set registration date for new customers
        if (!customerData.customer_id) {
            customerData.registration_date = new Date().toISOString().split('T')[0];
        }

        try {
            if (customerData.customer_id) {
                // Update existing customer
                await dbManager.update('customers', customerData);
                this.showAlert('Customer updated successfully!', 'success');
            } else {
                // Add new customer
                customerData.customer_id = await dbManager.generateId('customers', 'CUST');
                await dbManager.insert('customers', customerData);
                this.showAlert('Customer added successfully!', 'success');
            }
            
            this.closeCustomerModal();
            await this.loadData();
        } catch (error) {
            console.error('Error saving customer:', error);
            this.showAlert('Error saving customer. Please try again.', 'danger');
        }
    }

    showAlert(message, type) {
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alert, mainContent.firstChild);

        setTimeout(() => alert.remove(), 3000);
    }

    closeCustomerModal() {
        document.getElementById('customer-modal').style.display = 'none';
        document.getElementById('customer-form').reset();
        document.getElementById('customer-id').value = '';
        document.getElementById('customer-modal-title').textContent = 'Add New Customer';
    }

    closeCustomerDetailsModal() {
        document.getElementById('customer-details-modal').style.display = 'none';
    }
}

// Global functions
function showAddCustomerModal() {
    document.getElementById('customer-modal').style.display = 'flex';
    document.getElementById('customer-form').reset();
    document.getElementById('customer-id').value = '';
    document.getElementById('customer-modal-title').textContent = 'Add New Customer';
}

function closeCustomerModal() {
    customerManager.closeCustomerModal();
}

function closeCustomerDetailsModal() {
    customerManager.closeCustomerDetailsModal();
}

async function editCustomer(customerId) {
    try {
        const customer = await dbManager.get('customers', customerId);
        if (!customer) {
            alert('Customer not found');
            return;
        }

        // Populate form with customer data
        document.getElementById('customer-id').value = customer.customer_id;
        document.getElementById('customer-name').value = customer.customer_name;
        document.getElementById('phone-number').value = customer.phone_number;
        document.getElementById('email').value = customer.email || '';
        document.getElementById('date-of-birth').value = customer.date_of_birth || '';
        document.getElementById('address').value = customer.address || '';
        document.getElementById('medical-conditions').value = customer.medical_conditions || '';
        document.getElementById('allergies').value = customer.allergies || '';
        document.getElementById('status').value = customer.status;
        document.getElementById('total-purchase-amount').value = customer.total_purchase_amount || 0;
        document.getElementById('loyalty-points').value = customer.loyalty_points || 0;

        // Show modal
        document.getElementById('customer-modal').style.display = 'flex';
        document.getElementById('customer-modal-title').textContent = 'Edit Customer';
    } catch (error) {
        console.error('Error loading customer for edit:', error);
        alert('Error loading customer data');
    }
}

async function viewCustomerDetails(customerId) {
    try {
        const customer = await dbManager.get('customers', customerId);
        if (!customer) {
            alert('Customer not found');
            return;
        }

        // Get customer's purchase history
        const customerSales = customerManager.sales.filter(sale => sale.customer_phone === customer.phone_number);
        
        const detailsContent = `
            <div class="row">
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Personal Information</h4>
                        </div>
                        <div class="card-body">
                            <p><strong>Name:</strong> ${customer.customer_name}</p>
                            <p><strong>Phone:</strong> ${customer.phone_number}</p>
                            <p><strong>Email:</strong> ${customer.email || 'Not provided'}</p>
                            <p><strong>Date of Birth:</strong> ${customer.date_of_birth ? new Date(customer.date_of_birth).toLocaleDateString() : 'Not provided'}</p>
                            <p><strong>Address:</strong> ${customer.address || 'Not provided'}</p>
                            <p><strong>Registration Date:</strong> ${new Date(customer.registration_date).toLocaleDateString()}</p>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Medical Information</h4>
                        </div>
                        <div class="card-body">
                            <p><strong>Medical Conditions:</strong> ${customer.medical_conditions || 'None reported'}</p>
                            <p><strong>Allergies:</strong> ${customer.allergies || 'None reported'}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Purchase Summary</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    <div class="stat-card">
                                        <div class="stat-value">Rs. ${(customer.total_purchase_amount || 0).toLocaleString()}</div>
                                        <div class="stat-label">Total Purchases</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="stat-card">
                                        <div class="stat-value">${customerSales.length}</div>
                                        <div class="stat-label">Total Orders</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="stat-card">
                                        <div class="stat-value">${customer.loyalty_points || 0}</div>
                                        <div class="stat-label">Loyalty Points</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="stat-card">
                                        <div class="stat-value">${customer.status}</div>
                                        <div class="stat-label">Status</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Recent Purchase History</h4>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Sale ID</th>
                                    <th>Medicine</th>
                                    <th>Quantity</th>
                                    <th>Amount</th>
                                    <th>Payment Method</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${customerSales.slice(0, 10).map(sale => `
                                    <tr>
                                        <td>${new Date(sale.date).toLocaleDateString()}</td>
                                        <td>${sale.sale_id}</td>
                                        <td>${sale.medicine_name}</td>
                                        <td>${sale.quantity}</td>
                                        <td>Rs. ${parseFloat(sale.final_total).toFixed(2)}</td>
                                        <td>${sale.payment_method}</td>
                                    </tr>
                                `).join('')}
                                ${customerSales.length === 0 ? '<tr><td colspan="6" class="text-center text-muted">No purchases found</td></tr>' : ''}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('customer-details-content').innerHTML = detailsContent;
        document.getElementById('customer-details-modal').style.display = 'flex';
    } catch (error) {
        console.error('Error loading customer details:', error);
        alert('Error loading customer details');
    }
}

async function deleteCustomer(customerId) {
    if (!confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
        return;
    }

    try {
        await dbManager.delete('customers', customerId);
        customerManager.showAlert('Customer deleted successfully!', 'success');
        await customerManager.loadData();
    } catch (error) {
        console.error('Error deleting customer:', error);
        customerManager.showAlert('Error deleting customer. Please try again.', 'danger');
    }
}

function exportCustomers() {
    try {
        const headers = [
            'Customer ID', 'Customer Name', 'Phone Number', 'Email', 'Date of Birth',
            'Address', 'Medical Conditions', 'Allergies', 'Registration Date',
            'Total Purchase Amount', 'Loyalty Points', 'Last Purchase Date', 'Status'
        ];
        
        let csvContent = headers.join(',') + '\n';
        
        customerManager.customers.forEach(customer => {
            const row = [
                customer.customer_id,
                `"${customer.customer_name}"`,
                customer.phone_number,
                `"${customer.email || ''}"`,
                customer.date_of_birth || '',
                `"${customer.address || ''}"`,
                `"${customer.medical_conditions || ''}"`,
                `"${customer.allergies || ''}"`,
                customer.registration_date || '',
                customer.total_purchase_amount || 0,
                customer.loyalty_points || 0,
                customer.last_purchase_date || '',
                customer.status
            ];
            csvContent += row.join(',') + '\n';
        });
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `customers_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        customerManager.showAlert('Customers exported successfully!', 'success');
    } catch (error) {
        console.error('Error exporting customers:', error);
        customerManager.showAlert('Error exporting customers. Please try again.', 'danger');
    }
}

// Initialize customer manager when page loads
let customerManager;
document.addEventListener('DOMContentLoaded', () => {
    customerManager = new CustomerManager();
});