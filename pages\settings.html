<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Aanabi Pharmacy</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Aanabi Pharmacy Management System</div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link active">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">⚙️ System Settings</h1>
            </div>
            <div class="card-body">
                <p class="text-muted">Configure your pharmacy management system settings</p>
            </div>
        </div>

        <!-- Settings Sections -->
        <div class="row">
            <!-- Pharmacy Information -->
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🏥 Pharmacy Information</h3>
                    </div>
                    <div class="card-body">
                        <form id="pharmacy-info-form">
                            <div class="form-group">
                                <label class="form-label">Pharmacy Name *</label>
                                <input type="text" id="pharmacy-name" name="pharmacy_name" class="form-input" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">License Number</label>
                                <input type="text" id="license-number" name="license_number" class="form-input">
                            </div>

                            <div class="form-group">
                                <label class="form-label">Address</label>
                                <textarea id="address" name="address" class="form-textarea" rows="3"></textarea>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Phone Number</label>
                                        <input type="tel" id="phone" name="phone" class="form-input">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Email</label>
                                        <input type="email" id="email" name="email" class="form-input">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">GST Number</label>
                                <input type="text" id="gst-number" name="gst_number" class="form-input">
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Save Pharmacy Info</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Tax & Pricing Settings -->
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">💰 Tax & Pricing Settings</h3>
                    </div>
                    <div class="card-body">
                        <form id="tax-pricing-form">
                            <div class="form-group">
                                <label class="form-label">VAT Rate (%)</label>
                                <input type="number" id="vat-rate" name="vat_rate" class="form-input" min="0" max="100" step="0.01" value="13">
                            </div>

                            <div class="form-group">
                                <label class="form-label">Service Tax (%)</label>
                                <input type="number" id="service-tax" name="service_tax" class="form-input" min="0" max="100" step="0.01" value="0">
                            </div>

                            <div class="form-group">
                                <label class="form-label">Default Profit Margin (%)</label>
                                <input type="number" id="default-margin" name="default_margin" class="form-input" min="0" max="1000" step="0.01" value="60">
                            </div>

                            <div class="form-group">
                                <label class="form-label">Currency Symbol</label>
                                <select id="currency-symbol" name="currency_symbol" class="form-select">
                                    <option value="Rs.">Rs. (Nepali Rupees)</option>
                                    <option value="$">$ (US Dollar)</option>
                                    <option value="€">€ (Euro)</option>
                                    <option value="£">£ (British Pound)</option>
                                    <option value="₹">₹ (Indian Rupee)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Save Tax Settings</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Alert Settings -->
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🚨 Alert Settings</h3>
                    </div>
                    <div class="card-body">
                        <form id="alert-settings-form">
                            <div class="form-group">
                                <label class="form-label">Low Stock Alert Threshold (Days)</label>
                                <input type="number" id="low-stock-days" name="low_stock_alert_days" class="form-input" min="1" max="365" value="30">
                                <small class="text-muted">Alert when stock will last for fewer than this many days</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Expiry Alert Threshold (Days)</label>
                                <input type="number" id="expiry-alert-days" name="expiry_alert_days" class="form-input" min="1" max="365" value="30">
                                <small class="text-muted">Alert when medicines expire within this many days</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Payment Due Alert (Days)</label>
                                <input type="number" id="payment-due-days" name="payment_due_alert_days" class="form-input" min="1" max="365" value="7">
                                <small class="text-muted">Alert for payments due within this many days</small>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Enable Email Alerts</label>
                                <select id="email-alerts" name="email_alerts" class="form-select">
                                    <option value="enabled">Enabled</option>
                                    <option value="disabled">Disabled</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Save Alert Settings</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- System Preferences -->
            <div class="col-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">🔧 System Preferences</h3>
                    </div>
                    <div class="card-body">
                        <form id="system-preferences-form">
                            <div class="form-group">
                                <label class="form-label">Date Format</label>
                                <select id="date-format" name="date_format" class="form-select">
                                    <option value="MM/DD/YYYY">MM/DD/YYYY (US Format)</option>
                                    <option value="DD/MM/YYYY">DD/MM/YYYY (International)</option>
                                    <option value="YYYY-MM-DD">YYYY-MM-DD (ISO Format)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Time Format</label>
                                <select id="time-format" name="time_format" class="form-select">
                                    <option value="12">12 Hour (AM/PM)</option>
                                    <option value="24">24 Hour</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Auto-Backup Frequency</label>
                                <select id="backup-frequency" name="backup_frequency" class="form-select">
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="monthly">Monthly</option>
                                    <option value="manual">Manual Only</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Theme</label>
                                <select id="theme" name="theme" class="form-select">
                                    <option value="light">Light Theme</option>
                                    <option value="dark">Dark Theme</option>
                                    <option value="auto">Auto (System Default)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Save Preferences</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Management -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">💾 Data Management</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>📥 Import Data</h4>
                                <p class="text-muted">Import medicines, customers, or suppliers from CSV files</p>
                                <button class="btn btn-primary" onclick="showImportModal()">Import Data</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>📤 Export Data</h4>
                                <p class="text-muted">Export all system data for backup or migration</p>
                                <button class="btn btn-success" onclick="exportAllData()">Export All Data</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>💾 Backup Database</h4>
                                <p class="text-muted">Create a complete backup of your database</p>
                                <button class="btn btn-warning" onclick="createBackup()">Create Backup</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <h4>🗑️ Clear Data</h4>
                                <p class="text-muted">Reset system data (Use with caution!)</p>
                                <button class="btn btn-danger" onclick="showClearDataModal()">Clear Data</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Information -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">ℹ️ System Information</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-4">
                        <table class="table">
                            <tbody>
                                <tr>
                                    <td><strong>System Version:</strong></td>
                                    <td>1.0.0</td>
                                </tr>
                                <tr>
                                    <td><strong>Database Size:</strong></td>
                                    <td id="database-size">Calculating...</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Backup:</strong></td>
                                    <td id="last-backup">Never</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Records:</strong></td>
                                    <td id="total-records">0</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-8">
                        <h4>📋 Recent Activity</h4>
                        <div id="recent-activity">
                            <p class="text-muted">Loading recent activity...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Import Data Modal -->
    <div id="import-modal" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">📥 Import Data</h3>
                <button class="modal-close" onclick="closeImportModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="import-form">
                    <div class="form-group">
                        <label class="form-label">Data Type</label>
                        <select id="import-type" name="import_type" class="form-select" required>
                            <option value="">Select Data Type</option>
                            <option value="medicines">Medicines</option>
                            <option value="customers">Customers</option>
                            <option value="suppliers">Suppliers</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label class="form-label">CSV File</label>
                        <input type="file" id="import-file" name="import_file" class="form-input" accept=".csv" required>
                        <small class="text-muted">Please ensure your CSV file follows the correct format</small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Import Data</button>
                        <button type="button" class="btn btn-danger" onclick="closeImportModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Clear Data Modal -->
    <div id="clear-data-modal" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title">🗑️ Clear System Data</h3>
                <button class="modal-close" onclick="closeClearDataModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <strong>⚠️ Warning:</strong> This action will permanently delete selected data and cannot be undone. Please create a backup before proceeding.
                </div>

                <form id="clear-data-form">
                    <div class="form-group">
                        <label class="form-label">Select data to clear:</label>
                        <div>
                            <label><input type="checkbox" name="clear_sales" value="sales"> Sales Data</label><br>
                            <label><input type="checkbox" name="clear_purchases" value="purchases"> Purchase Data</label><br>
                            <label><input type="checkbox" name="clear_customers" value="customers"> Customer Data</label><br>
                            <label><input type="checkbox" name="clear_inventory" value="inventory"> Inventory Data</label><br>
                            <label><input type="checkbox" name="clear_all" value="all"> Clear All Data</label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Type "CONFIRM" to proceed:</label>
                        <input type="text" id="confirm-text" class="form-input" placeholder="Type CONFIRM">
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-danger">Clear Selected Data</button>
                        <button type="button" class="btn btn-secondary" onclick="closeClearDataModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/settings.js"></script>
</body>
</html>