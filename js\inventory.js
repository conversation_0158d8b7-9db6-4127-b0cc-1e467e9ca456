// Inventory management functionality

class InventoryManager {
    constructor() {
        this.inventory = [];
        this.medicines = [];
        this.suppliers = [];
        this.filteredInventory = [];
        this.currentFilter = 'all';
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadData() {
        try {
            // Wait for database to be ready
            if (!dbManager.db) {
                setTimeout(() => this.loadData(), 1000);
                return;
            }

            this.inventory = await dbManager.getAll('inventory');
            this.medicines = await dbManager.getAll('medicines');
            this.suppliers = await dbManager.getAll('suppliers');
            
            this.populateDropdowns();
            this.filteredInventory = [...this.inventory];
            this.renderInventory();
            this.updateStatistics();
            this.setTodayAsDefault();
        } catch (error) {
            console.error('Error loading inventory data:', error);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('inventory-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.filterInventoryBySearch(e.target.value));
        }

        // Form submission
        const form = document.getElementById('inventory-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    populateDropdowns() {
        // Populate medicine dropdown
        const medicineSelect = document.getElementById('medicine-select');
        if (medicineSelect) {
            medicineSelect.innerHTML = '<option value="">Select Medicine</option>';
            this.medicines.forEach(medicine => {
                const option = document.createElement('option');
                option.value = medicine.medicine_id;
                option.textContent = medicine.medicine_name;
                medicineSelect.appendChild(option);
            });
        }

        // Populate supplier dropdown
        const supplierSelect = document.getElementById('supplier-select');
        if (supplierSelect) {
            supplierSelect.innerHTML = '<option value="">Select Supplier</option>';
            this.suppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.supplier_name;
                option.textContent = supplier.supplier_name;
                supplierSelect.appendChild(option);
            });
        }
    }

    setTodayAsDefault() {
        const today = new Date().toISOString().split('T')[0];
        const purchaseDateInput = document.getElementById('purchase-date');
        if (purchaseDateInput) {
            purchaseDateInput.value = today;
        }
    }

    filterInventoryBySearch(searchTerm) {
        const term = searchTerm.toLowerCase();
        let baseInventory = this.inventory;

        // Apply current filter first
        if (this.currentFilter !== 'all') {
            baseInventory = this.getFilteredInventory(this.currentFilter);
        }

        this.filteredInventory = baseInventory.filter(item => 
            item.medicine_name.toLowerCase().includes(term) ||
            item.batch_number.toLowerCase().includes(term) ||
            item.supplier_name.toLowerCase().includes(term) ||
            item.inventory_id.toLowerCase().includes(term)
        );
        
        this.renderInventory();
    }

    getFilteredInventory(filter) {
        const today = new Date();
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

        switch (filter) {
            case 'good':
                return this.inventory.filter(item => {
                    const expiryDate = new Date(item.expiry_date);
                    return expiryDate > thirtyDaysFromNow;
                });
            case 'expiring':
                return this.inventory.filter(item => {
                    const expiryDate = new Date(item.expiry_date);
                    return expiryDate <= thirtyDaysFromNow && expiryDate > today;
                });
            case 'expired':
                return this.inventory.filter(item => {
                    const expiryDate = new Date(item.expiry_date);
                    return expiryDate <= today;
                });
            default:
                return this.inventory;
        }
    }

    renderInventory() {
        const tbody = document.getElementById('inventory-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.filteredInventory.length === 0) {
            tbody.innerHTML = '<tr><td colspan="12" class="text-center text-muted">No inventory items found</td></tr>';
            return;
        }

        this.filteredInventory.forEach(item => {
            const expiryDate = new Date(item.expiry_date);
            const today = new Date();
            const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
            
            let status = 'Good';
            let statusClass = 'status-active';
            
            if (daysLeft < 0) {
                status = 'Expired';
                statusClass = 'status-overdue';
            } else if (daysLeft <= 30) {
                status = 'Expiring Soon';
                statusClass = 'status-pending';
            }

            const totalValue = (item.current_stock * item.unit_cost).toFixed(2);

            const row = document.createElement('tr');
            
            // Add row styling based on status
            if (status === 'Expired') {
                row.style.backgroundColor = '#ffebee';
            } else if (status === 'Expiring Soon') {
                row.style.backgroundColor = '#fff3e0';
            }

            row.innerHTML = `
                <td>${item.inventory_id}</td>
                <td>${item.medicine_name}</td>
                <td>${item.batch_number}</td>
                <td>${new Date(item.manufacturing_date).toLocaleDateString()}</td>
                <td>${expiryDate.toLocaleDateString()}</td>
                <td>
                    <span class="${daysLeft < 0 ? 'text-danger' : daysLeft <= 30 ? 'text-warning' : 'text-success'}">
                        ${daysLeft < 0 ? 'Expired' : daysLeft + ' days'}
                    </span>
                </td>
                <td>${item.current_stock}</td>
                <td>Rs. ${parseFloat(item.unit_cost).toFixed(2)}</td>
                <td>Rs. ${totalValue}</td>
                <td>${item.supplier_name}</td>
                <td><span class="status ${statusClass}">${status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="editInventory('${item.inventory_id}')">Edit</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteInventory('${item.inventory_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateStatistics() {
        const today = new Date();
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

        const totalItems = this.inventory.length;
        const goodStock = this.inventory.filter(item => {
            const expiryDate = new Date(item.expiry_date);
            return expiryDate > thirtyDaysFromNow;
        }).length;
        
        const expiringSoon = this.inventory.filter(item => {
            const expiryDate = new Date(item.expiry_date);
            return expiryDate <= thirtyDaysFromNow && expiryDate > today;
        }).length;
        
        const expiredItems = this.inventory.filter(item => {
            const expiryDate = new Date(item.expiry_date);
            return expiryDate <= today;
        }).length;

        document.getElementById('total-items').textContent = totalItems;
        document.getElementById('good-stock').textContent = goodStock;
        document.getElementById('expiring-soon').textContent = expiringSoon;
        document.getElementById('expired-items').textContent = expiredItems;
    }

    async handleFormSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const inventoryData = Object.fromEntries(formData.entries());
        
        // Get medicine name
        const medicine = this.medicines.find(m => m.medicine_id === inventoryData.medicine_id);
        if (!medicine) {
            this.showAlert('Please select a valid medicine', 'danger');
            return;
        }

        inventoryData.medicine_name = medicine.medicine_name;
        inventoryData.current_stock = parseInt(inventoryData.current_stock);
        inventoryData.unit_cost = parseFloat(inventoryData.unit_cost);

        try {
            if (inventoryData.inventory_id) {
                // Update existing inventory
                await dbManager.update('inventory', inventoryData);
                this.showAlert('Inventory updated successfully!', 'success');
            } else {
                // Add new inventory
                inventoryData.inventory_id = await dbManager.generateId('inventory', 'INV');
                await dbManager.insert('inventory', inventoryData);
                
                // Update medicine stock
                medicine.current_stock = (medicine.current_stock || 0) + inventoryData.current_stock;
                await dbManager.update('medicines', medicine);
                
                this.showAlert('Inventory added successfully!', 'success');
            }
            
            this.closeInventoryModal();
            await this.loadData();
        } catch (error) {
            console.error('Error saving inventory:', error);
            this.showAlert('Error saving inventory. Please try again.', 'danger');
        }
    }

    showAlert(message, type) {
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alert, mainContent.firstChild);

        setTimeout(() => alert.remove(), 3000);
    }

    closeInventoryModal() {
        document.getElementById('inventory-modal').style.display = 'none';
        document.getElementById('inventory-form').reset();
        document.getElementById('inventory-id').value = '';
        document.getElementById('inventory-modal-title').textContent = 'Add New Stock';
        this.setTodayAsDefault();
    }
}

// Global functions
function showAddInventoryModal() {
    document.getElementById('inventory-modal').style.display = 'flex';
    document.getElementById('inventory-form').reset();
    document.getElementById('inventory-id').value = '';
    document.getElementById('inventory-modal-title').textContent = 'Add New Stock';
    inventoryManager.setTodayAsDefault();
}

function closeInventoryModal() {
    inventoryManager.closeInventoryModal();
}

function loadMedicineInfo() {
    const medicineId = document.getElementById('medicine-select').value;
    const medicine = inventoryManager.medicines.find(m => m.medicine_id === medicineId);
    
    if (medicine) {
        document.getElementById('medicine-name').value = medicine.medicine_name;
        document.getElementById('unit-cost').value = medicine.purchase_price || '';
    } else {
        document.getElementById('medicine-name').value = '';
        document.getElementById('unit-cost').value = '';
    }
}

function filterInventory(filter) {
    // Update active button
    document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
    
    inventoryManager.currentFilter = filter;
    inventoryManager.filteredInventory = inventoryManager.getFilteredInventory(filter);
    inventoryManager.renderInventory();
}

async function editInventory(inventoryId) {
    try {
        const inventory = await dbManager.get('inventory', inventoryId);
        if (!inventory) {
            alert('Inventory item not found');
            return;
        }

        // Populate form with inventory data
        document.getElementById('inventory-id').value = inventory.inventory_id;
        document.getElementById('medicine-select').value = inventory.medicine_id;
        document.getElementById('medicine-name').value = inventory.medicine_name;
        document.getElementById('batch-number').value = inventory.batch_number;
        document.getElementById('current-stock').value = inventory.current_stock;
        document.getElementById('manufacturing-date').value = inventory.manufacturing_date;
        document.getElementById('expiry-date').value = inventory.expiry_date;
        document.getElementById('unit-cost').value = inventory.unit_cost;
        document.getElementById('supplier-select').value = inventory.supplier_name;
        document.getElementById('purchase-date').value = inventory.purchase_date || '';

        // Show modal
        document.getElementById('inventory-modal').style.display = 'flex';
        document.getElementById('inventory-modal-title').textContent = 'Edit Inventory';
    } catch (error) {
        console.error('Error loading inventory for edit:', error);
        alert('Error loading inventory data');
    }
}

async function deleteInventory(inventoryId) {
    if (!confirm('Are you sure you want to delete this inventory item? This action cannot be undone.')) {
        return;
    }

    try {
        // Get inventory item to update medicine stock
        const inventory = await dbManager.get('inventory', inventoryId);
        if (inventory) {
            // Decrease medicine stock
            const medicine = await dbManager.get('medicines', inventory.medicine_id);
            if (medicine) {
                medicine.current_stock = Math.max(0, medicine.current_stock - inventory.current_stock);
                await dbManager.update('medicines', medicine);
            }
        }

        await dbManager.delete('inventory', inventoryId);
        inventoryManager.showAlert('Inventory item deleted successfully!', 'success');
        await inventoryManager.loadData();
    } catch (error) {
        console.error('Error deleting inventory:', error);
        inventoryManager.showAlert('Error deleting inventory item. Please try again.', 'danger');
    }
}

function exportInventory() {
    try {
        const headers = [
            'Inventory ID', 'Medicine Name', 'Batch Number', 'Manufacturing Date',
            'Expiry Date', 'Current Stock', 'Unit Cost', 'Total Value',
            'Supplier Name', 'Purchase Date', 'Status'
        ];
        
        let csvContent = headers.join(',') + '\n';
        
        inventoryManager.inventory.forEach(item => {
            const expiryDate = new Date(item.expiry_date);
            const today = new Date();
            const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
            
            let status = 'Good';
            if (daysLeft < 0) {
                status = 'Expired';
            } else if (daysLeft <= 30) {
                status = 'Expiring Soon';
            }

            const totalValue = (item.current_stock * item.unit_cost).toFixed(2);
            
            const row = [
                item.inventory_id,
                `"${item.medicine_name}"`,
                item.batch_number,
                item.manufacturing_date,
                item.expiry_date,
                item.current_stock,
                item.unit_cost,
                totalValue,
                `"${item.supplier_name}"`,
                item.purchase_date || '',
                status
            ];
            csvContent += row.join(',') + '\n';
        });
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `inventory_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        inventoryManager.showAlert('Inventory exported successfully!', 'success');
    } catch (error) {
        console.error('Error exporting inventory:', error);
        inventoryManager.showAlert('Error exporting inventory. Please try again.', 'danger');
    }
}

// Initialize inventory manager when page loads
let inventoryManager;
document.addEventListener('DOMContentLoaded', () => {
    inventoryManager = new InventoryManager();
});