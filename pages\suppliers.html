<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Supplier Management - Aanabi Pharmacy</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Aanabi Pharmacy Management System</div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link active">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">🏭 Supplier Management</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <div class="search-container">
                            <input type="text" id="supplier-search" class="search-input" placeholder="Search suppliers by name, contact person, or GST number...">
                        </div>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-primary" onclick="showAddSupplierModal()">➕ Add Supplier</button>
                        <button class="btn btn-warning" onclick="exportSuppliers()">📤 Export</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supplier Statistics -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <span class="stat-icon">🏭</span>
                <div class="stat-value" id="total-suppliers">0</div>
                <div class="stat-label">Total Suppliers</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-value" id="active-suppliers">0</div>
                <div class="stat-label">Active Suppliers</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">💰</span>
                <div class="stat-value" id="total-purchases">Rs. 0</div>
                <div class="stat-label">Total Purchases</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">⚠️</span>
                <div class="stat-value" id="outstanding-amount">Rs. 0</div>
                <div class="stat-label">Outstanding Amount</div>
            </div>
        </div>

        <!-- Supplier List -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Supplier Database</h3>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Supplier ID</th>
                                <th>Supplier Name</th>
                                <th>Contact Person</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>GST Number</th>
                                <th>Payment Terms</th>
                                <th>Credit Limit</th>
                                <th>Outstanding</th>
                                <th>Total Purchases</th>
                                <th>Last Purchase</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="suppliers-tbody">
                            <!-- Supplier data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Supplier Modal -->
    <div id="supplier-modal" class="modal-overlay" style="display: none;">
        <div class="modal" style="max-width: 700px;">
            <div class="modal-header">
                <h3 class="modal-title" id="supplier-modal-title">Add New Supplier</h3>
                <button class="modal-close" onclick="closeSupplierModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="supplier-form">
                    <input type="hidden" id="supplier-id" name="supplier_id">
                    
                    <!-- Basic Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Basic Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Supplier Name *</label>
                                        <input type="text" id="supplier-name" name="supplier_name" class="form-input" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Contact Person *</label>
                                        <input type="text" id="contact-person" name="contact_person" class="form-input" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Phone Number *</label>
                                        <input type="tel" id="phone-number" name="phone_number" class="form-input" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Email</label>
                                        <input type="email" id="email" name="email" class="form-input">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Address</label>
                                <textarea id="address" name="address" class="form-textarea" rows="2"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Business Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Business Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">GST Number</label>
                                        <input type="text" id="gst-number" name="gst_number" class="form-input">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Payment Terms *</label>
                                        <select id="payment-terms" name="payment_terms" class="form-select" required>
                                            <option value="">Select Payment Terms</option>
                                            <option value="Cash">Cash</option>
                                            <option value="Net 15">Net 15</option>
                                            <option value="Net 30">Net 30</option>
                                            <option value="Net 45">Net 45</option>
                                            <option value="Net 60">Net 60</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Credit Limit (Rs.)</label>
                                        <input type="number" id="credit-limit" name="credit_limit" class="form-input" min="0" value="0">
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Outstanding Amount</label>
                                        <input type="number" id="outstanding-amount" name="outstanding_amount" class="form-input" value="0" readonly>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Status</label>
                                        <select id="status" name="status" class="form-select">
                                            <option value="Active">Active</option>
                                            <option value="Inactive">Inactive</option>
                                            <option value="Blacklisted">Blacklisted</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Save Supplier</button>
                        <button type="button" class="btn btn-danger" onclick="closeSupplierModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Supplier Details Modal -->
    <div id="supplier-details-modal" class="modal-overlay" style="display: none;">
        <div class="modal" style="max-width: 900px;">
            <div class="modal-header">
                <h3 class="modal-title">Supplier Details</h3>
                <button class="modal-close" onclick="closeSupplierDetailsModal()">&times;</button>
            </div>
            <div class="modal-body" id="supplier-details-content">
                <!-- Supplier details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/suppliers.js"></script>
</body>
</html>