<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Management - Aanabi Pharmacy</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Aanabi Pharmacy Management System</div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link active">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">👥 Customer Management</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <div class="search-container">
                            <input type="text" id="customer-search" class="search-input" placeholder="Search customers by name, phone, or email...">
                        </div>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-primary" onclick="showAddCustomerModal()">➕ Add Customer</button>
                        <button class="btn btn-warning" onclick="exportCustomers()">📤 Export</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Statistics -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <span class="stat-icon">👥</span>
                <div class="stat-value" id="total-customers">0</div>
                <div class="stat-label">Total Customers</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">⭐</span>
                <div class="stat-value" id="vip-customers">0</div>
                <div class="stat-label">VIP Customers</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">💰</span>
                <div class="stat-value" id="total-revenue">Rs. 0</div>
                <div class="stat-label">Total Revenue</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">🏆</span>
                <div class="stat-value" id="loyalty-points">0</div>
                <div class="stat-label">Total Loyalty Points</div>
            </div>
        </div>

        <!-- Customer List -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Customer Database</h3>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Customer ID</th>
                                <th>Name</th>
                                <th>Phone</th>
                                <th>Email</th>
                                <th>Address</th>
                                <th>Date of Birth</th>
                                <th>Medical Conditions</th>
                                <th>Allergies</th>
                                <th>Total Purchases</th>
                                <th>Loyalty Points</th>
                                <th>Last Purchase</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="customers-tbody">
                            <!-- Customer data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Customer Modal -->
    <div id="customer-modal" class="modal-overlay" style="display: none;">
        <div class="modal" style="max-width: 700px;">
            <div class="modal-header">
                <h3 class="modal-title" id="customer-modal-title">Add New Customer</h3>
                <button class="modal-close" onclick="closeCustomerModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="customer-form">
                    <input type="hidden" id="customer-id" name="customer_id">
                    
                    <!-- Personal Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Personal Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Customer Name *</label>
                                        <input type="text" id="customer-name" name="customer_name" class="form-input" required>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Phone Number *</label>
                                        <input type="tel" id="phone-number" name="phone_number" class="form-input" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Email</label>
                                        <input type="email" id="email" name="email" class="form-input">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Date of Birth</label>
                                        <input type="date" id="date-of-birth" name="date_of_birth" class="form-input">
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label class="form-label">Address</label>
                                <textarea id="address" name="address" class="form-textarea" rows="2"></textarea>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Medical Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Medical Conditions</label>
                                        <textarea id="medical-conditions" name="medical_conditions" class="form-textarea" rows="3" placeholder="List any medical conditions..."></textarea>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Allergies</label>
                                        <textarea id="allergies" name="allergies" class="form-textarea" rows="3" placeholder="List any known allergies..."></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Account Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Status</label>
                                        <select id="status" name="status" class="form-select">
                                            <option value="Active">Active</option>
                                            <option value="Inactive">Inactive</option>
                                            <option value="VIP">VIP</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Total Purchase Amount</label>
                                        <input type="number" id="total-purchase-amount" name="total_purchase_amount" class="form-input" step="0.01" value="0" readonly>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Loyalty Points</label>
                                        <input type="number" id="loyalty-points" name="loyalty_points" class="form-input" value="0" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Save Customer</button>
                        <button type="button" class="btn btn-danger" onclick="closeCustomerModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Customer Details Modal -->
    <div id="customer-details-modal" class="modal-overlay" style="display: none;">
        <div class="modal" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title">Customer Details</h3>
                <button class="modal-close" onclick="closeCustomerDetailsModal()">&times;</button>
            </div>
            <div class="modal-body" id="customer-details-content">
                <!-- Customer details will be populated here -->
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/customers.js"></script>
</body>
</html>