<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventory Management - Aanabi Pharmacy</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Aanabi Pharmacy Management System</div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link active">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">📦 Inventory Management</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <div class="search-container">
                            <input type="text" id="inventory-search" class="search-input" placeholder="Search by medicine name, batch number, or supplier...">
                        </div>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-primary" onclick="showAddInventoryModal()">➕ Add Stock</button>
                        <button class="btn btn-warning" onclick="exportInventory()">📤 Export</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inventory Statistics -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <span class="stat-icon">📦</span>
                <div class="stat-value" id="total-items">0</div>
                <div class="stat-label">Total Items</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-value" id="good-stock">0</div>
                <div class="stat-label">Good Stock</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">⚠️</span>
                <div class="stat-value" id="expiring-soon">0</div>
                <div class="stat-label">Expiring Soon</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">❌</span>
                <div class="stat-value" id="expired-items">0</div>
                <div class="stat-label">Expired Items</div>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="card">
            <div class="card-body">
                <div class="filter-tabs">
                    <button class="btn btn-primary filter-btn active" onclick="filterInventory('all')">All Items</button>
                    <button class="btn btn-success filter-btn" onclick="filterInventory('good')">Good Stock</button>
                    <button class="btn btn-warning filter-btn" onclick="filterInventory('expiring')">Expiring Soon</button>
                    <button class="btn btn-danger filter-btn" onclick="filterInventory('expired')">Expired</button>
                </div>
            </div>
        </div>

        <!-- Inventory List -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Inventory Details</h3>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Inventory ID</th>
                                <th>Medicine Name</th>
                                <th>Batch Number</th>
                                <th>Manufacturing Date</th>
                                <th>Expiry Date</th>
                                <th>Days Left</th>
                                <th>Current Stock</th>
                                <th>Unit Cost</th>
                                <th>Total Value</th>
                                <th>Supplier</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="inventory-tbody">
                            <!-- Inventory data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Inventory Modal -->
    <div id="inventory-modal" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="inventory-modal-title">Add New Stock</h3>
                <button class="modal-close" onclick="closeInventoryModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="inventory-form">
                    <input type="hidden" id="inventory-id" name="inventory_id">
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Medicine *</label>
                                <select id="medicine-select" name="medicine_id" class="form-select" required onchange="loadMedicineInfo()">
                                    <option value="">Select Medicine</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Medicine Name</label>
                                <input type="text" id="medicine-name" class="form-input" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Batch Number *</label>
                                <input type="text" id="batch-number" name="batch_number" class="form-input" required>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Current Stock *</label>
                                <input type="number" id="current-stock" name="current_stock" class="form-input" min="0" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Manufacturing Date *</label>
                                <input type="date" id="manufacturing-date" name="manufacturing_date" class="form-input" required>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Expiry Date *</label>
                                <input type="date" id="expiry-date" name="expiry_date" class="form-input" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Unit Cost (Rs.) *</label>
                                <input type="number" id="unit-cost" name="unit_cost" class="form-input" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Supplier *</label>
                                <select id="supplier-select" name="supplier_name" class="form-select" required>
                                    <option value="">Select Supplier</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Purchase Date</label>
                        <input type="date" id="purchase-date" name="purchase_date" class="form-input" value="">
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Save Inventory</button>
                        <button type="button" class="btn btn-danger" onclick="closeInventoryModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/inventory.js"></script>
</body>
</html>