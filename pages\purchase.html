<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchase Management - Aanabi Pharmacy</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Aanabi Pharmacy Management System</div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link active">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">🛒 Purchase Management</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <div class="search-container">
                            <input type="text" id="purchase-search" class="search-input" placeholder="Search purchases by ID, supplier, or medicine...">
                        </div>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-primary" onclick="showAddPurchaseModal()">➕ New Purchase</button>
                        <button class="btn btn-warning" onclick="exportPurchases()">📤 Export</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Purchase Statistics -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <span class="stat-icon">🛒</span>
                <div class="stat-value" id="total-purchases-count">0</div>
                <div class="stat-label">Total Purchases</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">💰</span>
                <div class="stat-value" id="total-purchase-amount">Rs. 0</div>
                <div class="stat-label">Total Amount</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">⏳</span>
                <div class="stat-value" id="pending-payments">0</div>
                <div class="stat-label">Pending Payments</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">📅</span>
                <div class="stat-value" id="this-month-purchases">Rs. 0</div>
                <div class="stat-label">This Month</div>
            </div>
        </div>

        <!-- Purchase List -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Purchase Orders</h3>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Purchase ID</th>
                                <th>Date</th>
                                <th>Supplier</th>
                                <th>Invoice Number</th>
                                <th>Medicine</th>
                                <th>Quantity</th>
                                <th>Unit Cost</th>
                                <th>Total Amount</th>
                                <th>Payment Terms</th>
                                <th>Due Date</th>
                                <th>Payment Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="purchases-tbody">
                            <!-- Purchase data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Add Purchase Modal -->
    <div id="purchase-modal" class="modal-overlay" style="display: none;">
        <div class="modal" style="max-width: 800px;">
            <div class="modal-header">
                <h3 class="modal-title" id="purchase-modal-title">New Purchase Order</h3>
                <button class="modal-close" onclick="closePurchaseModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="purchase-form">
                    <input type="hidden" id="purchase-id" name="purchase_id">
                    
                    <!-- Supplier Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Supplier Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Supplier *</label>
                                        <select id="supplier-select" name="supplier_name" class="form-select" required onchange="loadSupplierInfo()">
                                            <option value="">Select Supplier</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Invoice Number</label>
                                        <input type="text" id="invoice-number" name="invoice_number" class="form-input">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Payment Terms</label>
                                        <select id="payment-terms" name="payment_terms" class="form-select" onchange="calculateDueDate()">
                                            <option value="Cash">Cash</option>
                                            <option value="Net 15">Net 15</option>
                                            <option value="Net 30">Net 30</option>
                                            <option value="Net 45">Net 45</option>
                                            <option value="Net 60">Net 60</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Due Date</label>
                                        <input type="date" id="payment-due-date" name="payment_due_date" class="form-input">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medicine Information -->
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Medicine Information</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Medicine *</label>
                                        <select id="medicine-select" name="medicine_id" class="form-select" required onchange="loadMedicineInfo()">
                                            <option value="">Select Medicine</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Quantity Ordered *</label>
                                        <input type="number" id="quantity-ordered" name="quantity_ordered" class="form-input" min="1" required onchange="calculateTotal()">
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Unit Cost (Rs.) *</label>
                                        <input type="number" id="unit-cost" name="unit_cost" class="form-input" step="0.01" required onchange="calculateTotal()">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Bonus Items</label>
                                        <input type="number" id="bonus-items" name="bonus_items" class="form-input" min="0" value="0">
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Batch Number</label>
                                        <input type="text" id="batch-number" name="batch_number" class="form-input">
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="form-group">
                                        <label class="form-label">Expiry Date</label>
                                        <input type="date" id="expiry-date" name="expiry_date" class="form-input">
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label" style="font-size: 1.2rem; font-weight: bold;">Total Amount (Rs.)</label>
                                        <input type="text" id="total-amount" class="form-input" readonly 
                                               style="font-size: 1.5rem; font-weight: bold; color: #2c3e50;">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Payment Status</label>
                                        <select id="payment-status" name="payment_status" class="form-select">
                                            <option value="Pending">Pending</option>
                                            <option value="Paid">Paid</option>
                                            <option value="Partial">Partial</option>
                                            <option value="Overdue">Overdue</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Save Purchase Order</button>
                                <button type="button" class="btn btn-danger" onclick="closePurchaseModal()">Cancel</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/purchase.js"></script>
</body>
</html>