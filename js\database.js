// Database Manager for Aanabi Pharmacy Management System
class DatabaseManager {
    constructor() {
        this.db = null;
        this.isInitialized = false;
        this.init();
    }

    isReady() {
        return this.isInitialized && this.db !== null;
    }

    async init() {
        try {
            console.log('Starting database initialization...');

            // Initialize IndexedDB database
            this.db = await this.openDatabase();
            console.log('Database connection established');

            await this.createTables();
            console.log('Database tables verified');

            await this.insertSampleData();
            console.log('Sample data insertion completed');

            this.isInitialized = true;
            console.log('Database initialized successfully');
        } catch (error) {
            console.error('Database initialization failed:', error);
            console.error('Error details:', error.message);

            // Set db to null to prevent further operations
            this.db = null;
            this.isInitialized = false;

            // Try to provide more specific error information
            if (error.name === 'QuotaExceededError') {
                console.error('Storage quota exceeded. Please clear browser data or free up space.');
            } else if (error.name === 'InvalidStateError') {
                console.error('Database is in an invalid state. Try clearing browser data.');
            } else if (error.name === 'UnknownError') {
                console.error('Unknown database error. This might be a browser compatibility issue.');
            }
        }
    }

    async openDatabase() {
        return new Promise((resolve, reject) => {
            // Check if IndexedDB is supported
            if (!window.indexedDB) {
                reject(new Error('IndexedDB is not supported in this browser'));
                return;
            }

            // For offline use, we'll use IndexedDB as SQLite alternative
            const request = indexedDB.open('AanabiPharmacyDB', 2);

            request.onerror = () => {
                console.error('Database opening failed:', request.error);
                reject(request.error);
            };

            request.onsuccess = () => {
                console.log('Database opened successfully');
                resolve(request.result);
            };

            request.onupgradeneeded = (event) => {
                console.log('Database upgrade needed, creating/updating schema...');
                const db = event.target.result;

                try {
                    // Create object stores (tables)
                    this.createObjectStores(db);
                    console.log('Database schema updated successfully');
                } catch (error) {
                    console.error('Error during database upgrade:', error);
                    reject(error);
                }
            };

            request.onblocked = () => {
                console.warn('Database upgrade blocked. Please close other tabs with this application.');
            };
        });
    }

    createObjectStores(db) {
        // Medicine Master table
        if (!db.objectStoreNames.contains('medicines')) {
            const medicineStore = db.createObjectStore('medicines', { keyPath: 'medicine_id' });
            medicineStore.createIndex('name', 'medicine_name', { unique: false });
            medicineStore.createIndex('category', 'category', { unique: false });
        }

        // Inventory Management table
        if (!db.objectStoreNames.contains('inventory')) {
            const inventoryStore = db.createObjectStore('inventory', { keyPath: 'inventory_id' });
            inventoryStore.createIndex('medicine_id', 'medicine_id', { unique: false });
            inventoryStore.createIndex('expiry_date', 'expiry_date', { unique: false });
        }

        // Sales & Billing table
        if (!db.objectStoreNames.contains('sales')) {
            const salesStore = db.createObjectStore('sales', { keyPath: 'sale_id' });
            salesStore.createIndex('date', 'date', { unique: false });
            salesStore.createIndex('customer_phone', 'customer_phone', { unique: false });
        }

        // Purchase Management table
        if (!db.objectStoreNames.contains('purchases')) {
            const purchaseStore = db.createObjectStore('purchases', { keyPath: 'purchase_id' });
            purchaseStore.createIndex('supplier_name', 'supplier_name', { unique: false });
            purchaseStore.createIndex('date', 'date', { unique: false });
        }

        // Customer Database table
        if (!db.objectStoreNames.contains('customers')) {
            const customerStore = db.createObjectStore('customers', { keyPath: 'customer_id' });
            customerStore.createIndex('phone', 'phone_number', { unique: true });
            customerStore.createIndex('name', 'customer_name', { unique: false });
        }

        // Supplier Management table
        if (!db.objectStoreNames.contains('suppliers')) {
            const supplierStore = db.createObjectStore('suppliers', { keyPath: 'supplier_id' });
            supplierStore.createIndex('name', 'supplier_name', { unique: false });
        }

        // Settings table
        if (!db.objectStoreNames.contains('settings')) {
            const settingsStore = db.createObjectStore('settings', { keyPath: 'key' });
        }
    }

    async createTables() {
        // Tables are created in createObjectStores method
        console.log('Tables created successfully');
    }

    async insertSampleData() {
        try {
            // Check if data already exists to avoid duplicates
            const existingMedicines = await this.getAll('medicines');
            if (existingMedicines.length > 0) {
                console.log('Sample data already exists, skipping insertion');
                return;
            }

            console.log('Inserting sample data for new database...');

            // Insert sample medicines
            const medicines = [
                {
                    medicine_id: 'MED001',
                    medicine_name: 'Paracetamol 500mg',
                    generic_name: 'Acetaminophen',
                    category: 'Pain Relief',
                    manufacturer: 'ABC Pharma',
                    pack_size: '10',
                    unit_type: 'Tablets',
                    min_stock_level: 50,
                    current_stock: 120,
                    purchase_price: 2.50,
                    selling_price: 4.00,
                    margin_percent: 60,
                    storage_location: 'A-1',
                    prescription_required: 'No',
                    status: 'Active'
                },
                {
                    medicine_id: 'MED002',
                    medicine_name: 'Amoxicillin 250mg',
                    generic_name: 'Amoxicillin',
                    category: 'Antibiotic',
                    manufacturer: 'XYZ Labs',
                    pack_size: '10',
                    unit_type: 'Capsules',
                    min_stock_level: 30,
                    current_stock: 75,
                    purchase_price: 15.00,
                    selling_price: 25.00,
                    margin_percent: 67,
                    storage_location: 'B-2',
                    prescription_required: 'Yes',
                    status: 'Active'
                },
                {
                    medicine_id: 'MED003',
                    medicine_name: 'Cough Syrup',
                    generic_name: 'Dextromethorphan',
                    category: 'Cold & Flu',
                    manufacturer: 'PQR Pharma',
                    pack_size: '100ml',
                    unit_type: 'Bottle',
                    min_stock_level: 20,
                    current_stock: 45,
                    purchase_price: 45.00,
                    selling_price: 75.00,
                    margin_percent: 67,
                    storage_location: 'C-3',
                    prescription_required: 'No',
                    status: 'Active'
                }
            ];

            for (const medicine of medicines) {
                try {
                    await this.insert('medicines', medicine);
                } catch (error) {
                    // Skip if already exists (duplicate key error)
                    if (!error.message.includes('already exists')) {
                        console.warn('Error inserting medicine:', medicine.medicine_id, error);
                    }
                }
            }

            // Insert sample customers
            const customers = [
                {
                    customer_id: 'CUST001',
                    customer_name: 'John Doe',
                    phone_number: '**********',
                    email: '<EMAIL>',
                    date_of_birth: '1985-01-15',
                    address: 'Kathmandu',
                    medical_conditions: 'Hypertension',
                    allergies: 'None',
                    registration_date: new Date().toISOString().split('T')[0],
                    last_purchase_date: new Date().toISOString().split('T')[0],
                    total_purchase_amount: 2500.00,
                    loyalty_points: 125,
                    status: 'Active'
                },
                {
                    customer_id: 'CUST002',
                    customer_name: 'Jane Smith',
                    phone_number: '**********',
                    email: '<EMAIL>',
                    date_of_birth: '1990-03-22',
                    address: 'Lalitpur',
                    medical_conditions: 'Diabetes',
                    allergies: 'Penicillin',
                    registration_date: new Date().toISOString().split('T')[0],
                    last_purchase_date: new Date().toISOString().split('T')[0],
                    total_purchase_amount: 4200.00,
                    loyalty_points: 210,
                    status: 'Active'
                }
            ];

            for (const customer of customers) {
                try {
                    await this.insert('customers', customer);
                } catch (error) {
                    // Skip if already exists (duplicate key error)
                    if (!error.message.includes('already exists')) {
                        console.warn('Error inserting customer:', customer.customer_id, error);
                    }
                }
            }

            // Insert sample suppliers
            const suppliers = [
                {
                    supplier_id: 'SUP001',
                    supplier_name: 'ABC Pharma',
                    contact_person: 'Mr. Rajesh Kumar',
                    phone_number: '01-4567890',
                    email: '<EMAIL>',
                    gst_number: '123456789',
                    address: 'Kathmandu',
                    payment_terms: 'Net 30',
                    credit_limit: 50000,
                    outstanding_amount: 500.00,
                    last_purchase_date: new Date().toISOString().split('T')[0],
                    total_purchase_amount: 25000.00,
                    status: 'Active'
                },
                {
                    supplier_id: 'SUP002',
                    supplier_name: 'XYZ Labs',
                    contact_person: 'Ms. Priya Sharma',
                    phone_number: '01-9876543',
                    email: '<EMAIL>',
                    gst_number: '987654321',
                    address: 'Lalitpur',
                    payment_terms: 'Net 15',
                    credit_limit: 75000,
                    outstanding_amount: 0.00,
                    last_purchase_date: new Date().toISOString().split('T')[0],
                    total_purchase_amount: 35000.00,
                    status: 'Active'
                }
            ];

            for (const supplier of suppliers) {
                try {
                    await this.insert('suppliers', supplier);
                } catch (error) {
                    // Skip if already exists (duplicate key error)
                    if (!error.message.includes('already exists')) {
                        console.warn('Error inserting supplier:', supplier.supplier_id, error);
                    }
                }
            }

            // Insert sample inventory
            const inventory = [
                {
                    inventory_id: 'INV001',
                    medicine_id: 'MED001',
                    batch_number: 'B001',
                    expiry_date: '2024-12-31',
                    quantity: 120,
                    purchase_price: 2.50,
                    selling_price: 4.00,
                    supplier_id: 'SUP001',
                    purchase_date: '2024-01-15',
                    status: 'Active'
                },
                {
                    inventory_id: 'INV002',
                    medicine_id: 'MED002',
                    batch_number: 'B002',
                    expiry_date: '2024-08-15',
                    quantity: 75,
                    purchase_price: 15.00,
                    selling_price: 25.00,
                    supplier_id: 'SUP002',
                    purchase_date: '2024-01-20',
                    status: 'Active'
                },
                {
                    inventory_id: 'INV003',
                    medicine_id: 'MED003',
                    batch_number: 'B003',
                    expiry_date: '2024-07-30',
                    quantity: 45,
                    purchase_price: 45.00,
                    selling_price: 75.00,
                    supplier_id: 'SUP001',
                    purchase_date: '2024-02-01',
                    status: 'Active'
                }
            ];

            for (const item of inventory) {
                try {
                    await this.insert('inventory', item);
                } catch (error) {
                    // Skip if already exists (duplicate key error)
                    if (!error.message.includes('already exists')) {
                        console.warn('Error inserting inventory item:', item.inventory_id, error);
                    }
                }
            }

            // Insert sample sales
            const sales = [
                {
                    sale_id: 'SALE001',
                    date: new Date().toISOString().split('T')[0],
                    customer_phone: '**********',
                    total_amount: 100.00,
                    discount: 5.00,
                    tax_amount: 12.35,
                    net_amount: 107.35,
                    payment_method: 'Cash',
                    status: 'Completed'
                },
                {
                    sale_id: 'SALE002',
                    date: new Date(Date.now() - 86400000).toISOString().split('T')[0], // Yesterday
                    customer_phone: '**********',
                    total_amount: 250.00,
                    discount: 10.00,
                    tax_amount: 31.20,
                    net_amount: 271.20,
                    payment_method: 'Card',
                    status: 'Completed'
                }
            ];

            for (const sale of sales) {
                try {
                    await this.insert('sales', sale);
                } catch (error) {
                    // Skip if already exists (duplicate key error)
                    if (!error.message.includes('already exists')) {
                        console.warn('Error inserting sale:', sale.sale_id, error);
                    }
                }
            }

            // Insert settings
            const settings = [
                { key: 'pharmacy_name', value: 'Aanabi Pharmacy' },
                { key: 'license_number', value: 'PH-2024-001' },
                { key: 'address', value: 'Main Street, Kathmandu, Nepal' },
                { key: 'phone', value: '01-4567890' },
                { key: 'email', value: '<EMAIL>' },
                { key: 'gst_number', value: '987654321' },
                { key: 'vat_rate', value: '13' },
                { key: 'default_margin', value: '60' },
                { key: 'low_stock_alert_days', value: '30' },
                { key: 'expiry_alert_days', value: '30' }
            ];

            for (const setting of settings) {
                try {
                    await this.insert('settings', setting);
                } catch (error) {
                    // Skip if already exists (duplicate key error)
                    if (!error.message.includes('already exists')) {
                        console.warn('Error inserting setting:', setting.key, error);
                    }
                }
            }

            console.log('Sample data insertion completed successfully');
        } catch (error) {
            console.error('Error inserting sample data:', error);
        }
    }

    async insert(storeName, data) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.add(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async update(storeName, data) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.put(data);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async delete(storeName, key) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(key);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async get(storeName, key) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(key);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async getAll(storeName) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async search(storeName, indexName, value) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);

            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
        });
    }

    async generateId(storeName, prefix) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }
        const allRecords = await this.getAll(storeName);
        const maxId = allRecords.reduce((max, record) => {
            const idNumber = parseInt(record[Object.keys(record)[0]].substring(prefix.length));
            return Math.max(max, idNumber);
        }, 0);
        return prefix + String(maxId + 1).padStart(3, '0');
    }
}

// Initialize database with retry mechanism
let dbManager;

async function initializeDatabaseWithRetry(maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            console.log(`Database initialization attempt ${attempt}/${maxRetries}`);
            dbManager = new DatabaseManager();

            // Wait a bit to see if initialization succeeds
            await new Promise(resolve => setTimeout(resolve, 2000));

            if (dbManager.isReady()) {
                console.log('Database initialization successful');
                return;
            } else {
                throw new Error('Database not ready after initialization');
            }
        } catch (error) {
            console.error(`Database initialization attempt ${attempt} failed:`, error);

            if (attempt === maxRetries) {
                console.error('All database initialization attempts failed');
                // Create a dummy dbManager to prevent undefined errors
                dbManager = {
                    isReady: () => false,
                    db: null,
                    getAll: () => Promise.reject(new Error('Database not available')),
                    get: () => Promise.reject(new Error('Database not available')),
                    insert: () => Promise.reject(new Error('Database not available')),
                    update: () => Promise.reject(new Error('Database not available')),
                    delete: () => Promise.reject(new Error('Database not available'))
                };
            } else {
                // Wait before retrying
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }
}

// Start database initialization
initializeDatabaseWithRetry();
