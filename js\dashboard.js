// Enhanced Dashboard functionality for Aanabi Pharmacy Management System

class Dashboard {
    constructor() {
        this.alertSettings = {
            lowStockDays: 30,
            expiryAlertDays: 30,
            paymentDueDays: 7
        };
        this.notifications = [];
        this.init();
    }

    async init() {
        // Wait for database to be ready
        const databaseReady = await this.waitForDatabase();

        if (!databaseReady) {
            console.error('Dashboard initialization aborted due to database failure');
            return; // Exit early if database is not ready
        }

        try {
            await this.loadAlertSettings();
            await this.updateDateTime();
            await this.loadDashboardStats();
            await this.loadAndProcessAlerts();
            await this.loadRecentSales();
            await this.loadLowStockItems();
            await this.loadExpiringItems();

            // Update time every second
            setInterval(() => this.updateDateTime(), 1000);

            // Refresh dashboard data every 30 seconds for real-time updates
            setInterval(() => this.refreshDashboard(), 30000);

            // Show notifications immediately
            this.showActiveNotifications();

            console.log('Dashboard initialized successfully');
        } catch (error) {
            console.error('Error during dashboard initialization:', error);
            this.showDatabaseError();
        }
    }

    async waitForDatabase() {
        // Wait for database to be initialized
        let attempts = 0;
        const maxAttempts = 200; // 20 seconds max wait

        console.log('Waiting for database to initialize...');

        // First, wait for dbManager to be defined
        while (typeof dbManager === 'undefined' && attempts < 50) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;
        }

        if (typeof dbManager === 'undefined') {
            console.error('dbManager is not defined - database script may not have loaded');
            this.showDatabaseError();
            return false;
        }

        console.log('dbManager found, waiting for initialization...');

        // Reset attempts counter for database ready check
        attempts = 0;

        while (!dbManager.isReady() && attempts < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, 100));
            attempts++;

            // Log progress every 3 seconds
            if (attempts % 30 === 0) {
                console.log(`Still waiting for database... (${attempts/10}s elapsed)`);
            }
        }

        if (!dbManager.isReady()) {
            console.error('Database failed to initialize within timeout period');
            // Show error message to user
            this.showDatabaseError();
            return false; // Return false to indicate failure
        }

        console.log('Database is ready!');
        return true; // Return true to indicate success
    }

    showDatabaseError() {
        const alertsContainer = document.getElementById('alerts-container');
        if (alertsContainer) {
            alertsContainer.innerHTML = `
                <div class="alert alert-danger">
                    <strong>⚠️ Database Error</strong><br>
                    The application database failed to initialize. This could be due to:<br>
                    • Browser storage limitations<br>
                    • Insufficient permissions<br>
                    • Corrupted database<br><br>
                    <button class="btn btn-sm btn-danger" onclick="location.reload()" style="margin-right: 10px;">
                        Refresh Page
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="dashboard.clearDatabase()">
                        Clear Database & Restart
                    </button>
                </div>
            `;
        }

        // Also show error in other dashboard sections
        this.showErrorInSections();
    }

    showErrorInSections() {
        const errorMessage = '<tr><td colspan="6" class="text-center text-danger">⚠️ Database not available</td></tr>';

        // Show error in tables
        const tables = [
            'recent-sales-tbody',
            'low-stock-tbody',
            'expiring-items-tbody'
        ];

        tables.forEach(tableId => {
            const tbody = document.getElementById(tableId);
            if (tbody) {
                tbody.innerHTML = errorMessage;
            }
        });

        // Show error in stats
        const stats = [
            'total-medicines',
            'low-stock-items',
            'expiring-items',
            'today-sales',
            'total-customers',
            'total-suppliers'
        ];

        stats.forEach(statId => {
            const element = document.getElementById(statId);
            if (element) {
                element.textContent = 'N/A';
            }
        });
    }

    async clearDatabase() {
        if (confirm('This will delete all data and restart the application. Are you sure?')) {
            try {
                // Delete the IndexedDB database
                const deleteRequest = indexedDB.deleteDatabase('AanabiPharmacyDB');
                deleteRequest.onsuccess = () => {
                    console.log('Database deleted successfully');
                    location.reload();
                };
                deleteRequest.onerror = () => {
                    console.error('Error deleting database');
                    alert('Error clearing database. Please try refreshing the page.');
                };
            } catch (error) {
                console.error('Error clearing database:', error);
                alert('Error clearing database. Please try refreshing the page.');
            }
        }
    }

    checkDatabaseReady() {
        if (!dbManager.isReady()) {
            throw new Error('Database not ready');
        }
    }

    async loadAlertSettings() {
        try {
            this.checkDatabaseReady();

            const settings = await dbManager.getAll('settings');
            settings.forEach(setting => {
                switch (setting.key) {
                    case 'low_stock_alert_days':
                        this.alertSettings.lowStockDays = parseInt(setting.value) || 30;
                        break;
                    case 'expiry_alert_days':
                        this.alertSettings.expiryAlertDays = parseInt(setting.value) || 30;
                        break;
                    case 'payment_due_alert_days':
                        this.alertSettings.paymentDueDays = parseInt(setting.value) || 7;
                        break;
                }
            });
        } catch (error) {
            console.error('Error loading alert settings:', error);
        }
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadDashboardStats() {
        try {
            this.checkDatabaseReady();

            // Load all data
            const medicines = await dbManager.getAll('medicines');
            const inventory = await dbManager.getAll('inventory');
            const sales = await dbManager.getAll('sales');
            const customers = await dbManager.getAll('customers');
            const suppliers = await dbManager.getAll('suppliers');

            // Calculate statistics
            const totalMedicines = medicines.length;
            
            // Low Stock Items (using alert threshold)
            const lowStockItems = medicines.filter(med => 
                med.current_stock <= med.min_stock_level
            );
            
            // Expiring Items (within alert threshold days)
            const today = new Date();
            const alertThresholdDate = new Date();
            alertThresholdDate.setDate(today.getDate() + this.alertSettings.expiryAlertDays);
            
            const expiringItems = inventory.filter(item => {
                const expiryDate = new Date(item.expiry_date);
                return expiryDate <= alertThresholdDate && expiryDate > today;
            });

            // Today's Sales
            const todayStr = today.toISOString().split('T')[0];
            const todaySales = sales.filter(sale => sale.date === todayStr);
            const todayTotal = todaySales.reduce((sum, sale) => sum + (parseFloat(sale.final_total) || 0), 0);

            // Update DOM elements
            document.getElementById('total-medicines').textContent = totalMedicines;
            document.getElementById('low-stock-items').textContent = lowStockItems.length;
            document.getElementById('expiring-items').textContent = expiringItems.length;
            document.getElementById('today-sales').textContent = `Rs. ${todayTotal.toLocaleString()}`;
            document.getElementById('total-customers').textContent = customers.length;
            document.getElementById('total-suppliers').textContent = suppliers.length;

        } catch (error) {
            console.error('Error loading dashboard stats:', error);
        }
    }

    async loadAndProcessAlerts() {
        try {
            this.checkDatabaseReady();
            this.notifications = []; // Clear existing notifications

            const medicines = await dbManager.getAll('medicines');
            const inventory = await dbManager.getAll('inventory');
            const purchases = await dbManager.getAll('purchases');
            const suppliers = await dbManager.getAll('suppliers');

            // 1. LOW STOCK ALERTS
            const lowStockItems = medicines.filter(med => med.current_stock <= med.min_stock_level);
            if (lowStockItems.length > 0) {
                this.notifications.push({
                    type: 'danger',
                    title: 'Critical Stock Alert',
                    message: `${lowStockItems.length} medicine(s) are running critically low on stock`,
                    action: 'View Low Stock Items',
                    actionUrl: '#low-stock-section',
                    icon: '📦',
                    priority: 'high',
                    count: lowStockItems.length
                });
            }

            // 2. EXPIRY ALERTS
            const today = new Date();
            const expiryThreshold = new Date();
            expiryThreshold.setDate(today.getDate() + this.alertSettings.expiryAlertDays);
            
            const expiringItems = inventory.filter(item => {
                const expiryDate = new Date(item.expiry_date);
                return expiryDate <= expiryThreshold && expiryDate > today;
            });

            const expiredItems = inventory.filter(item => {
                const expiryDate = new Date(item.expiry_date);
                return expiryDate <= today;
            });

            if (expiringItems.length > 0) {
                this.notifications.push({
                    type: 'warning',
                    title: 'Expiry Warning',
                    message: `${expiringItems.length} item(s) will expire within ${this.alertSettings.expiryAlertDays} days`,
                    action: 'View Expiring Items',
                    actionUrl: '#expiring-items-section',
                    icon: '⏰',
                    priority: 'medium',
                    count: expiringItems.length
                });
            }

            if (expiredItems.length > 0) {
                this.notifications.push({
                    type: 'danger',
                    title: 'Expired Items Alert',
                    message: `${expiredItems.length} item(s) have already expired and need immediate attention`,
                    action: 'View Expired Items',
                    actionUrl: 'pages/inventory.html',
                    icon: '❌',
                    priority: 'high',
                    count: expiredItems.length
                });
            }

            // 3. UNPAID BILLS ALERT
            const overdueThreshold = new Date();
            overdueThreshold.setDate(today.getDate() - this.alertSettings.paymentDueDays);
            
            const overduePurchases = purchases.filter(purchase => {
                if (purchase.payment_status !== 'Pending') return false;
                const dueDate = new Date(purchase.payment_due_date);
                return dueDate < overdueThreshold;
            });

            if (overduePurchases.length > 0) {
                const overdueAmount = overduePurchases.reduce((sum, purchase) => 
                    sum + (parseFloat(purchase.total_amount) || 0), 0);
                
                this.notifications.push({
                    type: 'danger',
                    title: 'Overdue Payments',
                    message: `${overduePurchases.length} payment(s) overdue by more than ${this.alertSettings.paymentDueDays} days. Total: Rs. ${overdueAmount.toLocaleString()}`,
                    action: 'View Overdue Payments',
                    actionUrl: 'pages/purchase.html',
                    icon: '💰',
                    priority: 'high',
                    count: overduePurchases.length
                });
            }

            // 4. SUPPLIER CREDIT LIMIT ALERTS
            const creditExceededSuppliers = suppliers.filter(supplier => 
                supplier.outstanding_amount > supplier.credit_limit && supplier.credit_limit > 0
            );

            if (creditExceededSuppliers.length > 0) {
                this.notifications.push({
                    type: 'warning',
                    title: 'Credit Limit Exceeded',
                    message: `${creditExceededSuppliers.length} supplier(s) have exceeded their credit limits`,
                    action: 'View Suppliers',
                    actionUrl: 'pages/suppliers.html',
                    icon: '🏭',
                    priority: 'medium',
                    count: creditExceededSuppliers.length
                });
            }

            // 5. NO SALES TODAY ALERT
            const todayStr = today.toISOString().split('T')[0];
            const todaySales = await dbManager.getAll('sales');
            const salesToday = todaySales.filter(sale => sale.date === todayStr);
            
            if (salesToday.length === 0 && today.getHours() > 10) { // Only show after 10 AM
                this.notifications.push({
                    type: 'info',
                    title: 'No Sales Today',
                    message: 'No sales recorded today. Consider checking if the system is being used properly.',
                    action: 'Record New Sale',
                    actionUrl: 'pages/sales.html',
                    icon: '📊',
                    priority: 'low',
                    count: 0
                });
            }

            // Update alerts display
            this.displayAlerts();

        } catch (error) {
            console.error('Error processing alerts:', error);
        }
    }

    displayAlerts() {
        const alertsContainer = document.getElementById('alerts-container');
        if (!alertsContainer) return;

        alertsContainer.innerHTML = '';

        if (this.notifications.length === 0) {
            alertsContainer.innerHTML = `
                <div class="alert alert-success">
                    <strong>✅ All Systems Running Smoothly!</strong><br>
                    No critical alerts at this time. Your pharmacy operations are on track.
                </div>
            `;
            return;
        }

        // Sort notifications by priority
        const priorityOrder = { 'high': 3, 'medium': 2, 'low': 1 };
        this.notifications.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);

        this.notifications.forEach(notification => {
            const alertElement = document.createElement('div');
            alertElement.className = `alert alert-${notification.type}`;
            alertElement.innerHTML = `
                <div style="display: flex; justify-content: between; align-items: center;">
                    <div style="flex: 1;">
                        <strong>${notification.icon} ${notification.title}</strong><br>
                        <span>${notification.message}</span>
                    </div>
                    <div style="margin-left: 10px;">
                        <a href="${notification.actionUrl}" class="btn btn-sm btn-${notification.type}" 
                           onclick="this.closest('.alert').style.display='none'">
                            ${notification.action}
                        </a>
                        <button class="btn btn-sm btn-secondary" onclick="this.closest('.alert').style.display='none'" 
                                style="margin-left: 5px;">Dismiss</button>
                    </div>
                </div>
            `;
            alertsContainer.appendChild(alertElement);
        });
    }

    showActiveNotifications() {
        // Show browser notifications for high priority alerts (if permission granted)
        if (Notification.permission === 'granted') {
            const highPriorityAlerts = this.notifications.filter(n => n.priority === 'high');
            highPriorityAlerts.forEach(alert => {
                new Notification(`Aanabi Pharmacy - ${alert.title}`, {
                    body: alert.message,
                    icon: '🏥'
                });
            });
        } else if (Notification.permission !== 'denied') {
            // Request permission for future notifications
            Notification.requestPermission();
        }
    }

    async loadRecentSales() {
        try {
            this.checkDatabaseReady();
            const tbody = document.getElementById('recent-sales-tbody');
            if (!tbody) return;

            tbody.innerHTML = '';

            const sales = await dbManager.getAll('sales');
            const recentSales = sales
                .sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time))
                .slice(0, 5);

            if (recentSales.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">No recent sales found</td></tr>';
                return;
            }

            recentSales.forEach(sale => {
                const row = document.createElement('tr');
                const saleDate = new Date(sale.date + ' ' + sale.time);
                const timeAgo = this.getTimeAgo(saleDate);
                
                row.innerHTML = `
                    <td>
                        <strong>${sale.sale_id}</strong><br>
                        <small class="text-muted">${timeAgo}</small>
                    </td>
                    <td>${new Date(sale.date).toLocaleDateString()}</td>
                    <td>
                        ${sale.customer_name || 'Walk-in Customer'}<br>
                        <small class="text-muted">${sale.customer_phone || 'No phone'}</small>
                    </td>
                    <td>
                        ${sale.medicine_name}<br>
                        <small class="text-muted">Qty: ${sale.quantity}</small>
                    </td>
                    <td>
                        <strong>Rs. ${parseFloat(sale.final_total || 0).toLocaleString()}</strong><br>
                        <small class="text-success">Profit: Rs. ${parseFloat(sale.profit_amount || 0).toFixed(2)}</small>
                    </td>
                    <td>
                        <span class="status status-paid">${sale.payment_method}</span>
                    </td>
                `;
                tbody.appendChild(row);
            });

        } catch (error) {
            console.error('Error loading recent sales:', error);
        }
    }

    async loadLowStockItems() {
        try {
            this.checkDatabaseReady();
            const tbody = document.getElementById('low-stock-tbody');
            if (!tbody) return;

            tbody.innerHTML = '';

            const medicines = await dbManager.getAll('medicines');
            const lowStockItems = medicines.filter(med => 
                med.current_stock <= med.min_stock_level
            ).slice(0, 5);

            if (lowStockItems.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-success">✅ All items are well stocked!</td></tr>';
                return;
            }

            lowStockItems.forEach(item => {
                const stockPercentage = ((item.current_stock / item.min_stock_level) * 100).toFixed(1);
                const urgencyClass = item.current_stock === 0 ? 'text-danger' : 
                                   item.current_stock < (item.min_stock_level * 0.5) ? 'text-danger' : 'text-warning';
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <strong>${item.medicine_name}</strong><br>
                        <small class="text-muted">${item.category} - ${item.manufacturer}</small>
                    </td>
                    <td>
                        <span class="${urgencyClass} font-weight-bold">${item.current_stock}</span><br>
                        <small class="text-muted">${stockPercentage}% of min level</small>
                    </td>
                    <td>
                        ${item.min_stock_level}<br>
                        <small class="text-muted">Required minimum</small>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-primary" onclick="reorderItem('${item.medicine_id}')">
                            📦 Reorder
                        </button><br>
                        <button class="btn btn-sm btn-warning mt-1" onclick="updateStock('${item.medicine_id}')">
                            ✏️ Update Stock
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

        } catch (error) {
            console.error('Error loading low stock items:', error);
        }
    }

    async loadExpiringItems() {
        try {
            this.checkDatabaseReady();
            const tbody = document.getElementById('expiring-items-tbody');
            if (!tbody) return;

            tbody.innerHTML = '';

            const inventory = await dbManager.getAll('inventory');
            const today = new Date();
            const alertThreshold = new Date();
            alertThreshold.setDate(today.getDate() + this.alertSettings.expiryAlertDays);
            
            const expiringItems = inventory.filter(item => {
                const expiryDate = new Date(item.expiry_date);
                return expiryDate <= alertThreshold && expiryDate > today;
            }).slice(0, 5);

            if (expiringItems.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center text-success">✅ No items expiring soon!</td></tr>';
                return;
            }

            expiringItems.forEach(item => {
                const expiryDate = new Date(item.expiry_date);
                const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
                const urgencyClass = daysLeft <= 7 ? 'text-danger' : 
                                   daysLeft <= 15 ? 'text-warning' : 'text-info';
                
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <strong>${item.medicine_name}</strong><br>
                        <small class="text-muted">Stock: ${item.current_stock} units</small>
                    </td>
                    <td>
                        <strong>${item.batch_number}</strong><br>
                        <small class="text-muted">${item.supplier_name}</small>
                    </td>
                    <td>
                        ${expiryDate.toLocaleDateString()}<br>
                        <small class="text-muted">Mfg: ${new Date(item.manufacturing_date).toLocaleDateString()}</small>
                    </td>
                    <td>
                        <span class="${urgencyClass} font-weight-bold">
                            ${daysLeft} day${daysLeft !== 1 ? 's' : ''}
                        </span><br>
                        <small class="${urgencyClass}">
                            ${daysLeft <= 7 ? '🚨 Critical' : daysLeft <= 15 ? '⚠️ Warning' : '📅 Monitor'}
                        </small>
                    </td>
                `;
                tbody.appendChild(row);
            });

        } catch (error) {
            console.error('Error loading expiring items:', error);
        }
    }

    getTimeAgo(date) {
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins} min${diffMins !== 1 ? 's' : ''} ago`;
        if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
        if (diffDays < 7) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
        return date.toLocaleDateString();
    }

    async refreshDashboard() {
        // Only refresh if database is ready
        if (!dbManager.isReady()) {
            console.warn('Skipping dashboard refresh - database not ready');
            return;
        }

        try {
            await this.loadDashboardStats();
            await this.loadAndProcessAlerts();
            await this.loadRecentSales();
            await this.loadLowStockItems();
            await this.loadExpiringItems();
        } catch (error) {
            console.error('Error during dashboard refresh:', error);
            // Don't show error UI during refresh, just log it
        }
    }
}

// Global functions
async function reorderItem(medicineId) {
    try {
        const medicine = await dbManager.get('medicines', medicineId);
        if (medicine) {
            const reorderQuantity = prompt(`Reorder ${medicine.medicine_name}?\n\nCurrent Stock: ${medicine.current_stock}\nMinimum Level: ${medicine.min_stock_level}\n\nEnter quantity to reorder:`, medicine.min_stock_level * 2);
            
            if (reorderQuantity && !isNaN(reorderQuantity) && parseInt(reorderQuantity) > 0) {
                // Redirect to purchase page with pre-filled medicine
                window.location.href = `pages/purchase.html?medicine=${medicineId}&quantity=${reorderQuantity}`;
            }
        }
    } catch (error) {
        console.error('Error reordering item:', error);
        alert('Error processing reorder request');
    }
}

async function updateStock(medicineId) {
    try {
        const medicine = await dbManager.get('medicines', medicineId);
        if (medicine) {
            const newStock = prompt(`Update stock for ${medicine.medicine_name}?\n\nCurrent Stock: ${medicine.current_stock}\n\nEnter new stock quantity:`, medicine.current_stock);
            
            if (newStock && !isNaN(newStock) && parseInt(newStock) >= 0) {
                medicine.current_stock = parseInt(newStock);
                await dbManager.update('medicines', medicine);
                
                // Refresh dashboard
                dashboard.refreshDashboard();
                alert(`Stock updated successfully!\n${medicine.medicine_name} now has ${newStock} units in stock.`);
            }
        }
    } catch (error) {
        console.error('Error updating stock:', error);
        alert('Error updating stock');
    }
}

// Scroll to section function
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    }
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing dashboard...');
    dashboard = new Dashboard();

    // Add click handlers for alert actions
    document.addEventListener('click', (e) => {
        if (e.target.getAttribute('href') && e.target.getAttribute('href').startsWith('#')) {
            e.preventDefault();
            const sectionId = e.target.getAttribute('href').substring(1);
            scrollToSection(sectionId);
        }
    });
});