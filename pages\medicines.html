<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medicine Management - Aanabi Pharmacy</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Aanabi Pharmacy Management System</div>
            <div class="header-info">
                <span id="current-date"></span> | <span id="current-time"></span>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="nav">
        <div class="nav-content">
            <ul class="nav-menu">
                <li class="nav-item"><a href="../index.html" class="nav-link">🏠 Dashboard</a></li>
                <li class="nav-item"><a href="medicines.html" class="nav-link active">💊 Medicines</a></li>
                <li class="nav-item"><a href="inventory.html" class="nav-link">📦 Inventory</a></li>
                <li class="nav-item"><a href="sales.html" class="nav-link">💳 Sales</a></li>
                <li class="nav-item"><a href="purchase.html" class="nav-link">🛒 Purchase</a></li>
                <li class="nav-item"><a href="customers.html" class="nav-link">👥 Customers</a></li>
                <li class="nav-item"><a href="suppliers.html" class="nav-link">🏭 Suppliers</a></li>
                <li class="nav-item"><a href="reports.html" class="nav-link">📊 Reports</a></li>
                <li class="nav-item"><a href="settings.html" class="nav-link">⚙️ Settings</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Page Header -->
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">💊 Medicine Management</h1>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <div class="search-container">
                            <input type="text" id="medicine-search" class="search-input" placeholder="Search medicines by name, category, or manufacturer...">
                        </div>
                    </div>
                    <div class="col-4">
                        <button class="btn btn-primary" onclick="showAddMedicineModal()">➕ Add New Medicine</button>
                        <button class="btn btn-success" onclick="exportMedicines()">📤 Export</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Medicine Statistics -->
        <div class="dashboard-stats">
            <div class="stat-card">
                <span class="stat-icon">💊</span>
                <div class="stat-value" id="total-medicines-count">0</div>
                <div class="stat-label">Total Medicines</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">✅</span>
                <div class="stat-value" id="active-medicines-count">0</div>
                <div class="stat-label">Active Medicines</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">📦</span>
                <div class="stat-value" id="low-stock-count">0</div>
                <div class="stat-label">Low Stock</div>
            </div>
            <div class="stat-card">
                <span class="stat-icon">💰</span>
                <div class="stat-value" id="total-inventory-value">Rs. 0</div>
                <div class="stat-label">Total Inventory Value</div>
            </div>
        </div>

        <!-- Medicine List -->
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Medicine Inventory</h3>
            </div>
            <div class="card-body">
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>Medicine ID</th>
                                <th>Name</th>
                                <th>Generic Name</th>
                                <th>Category</th>
                                <th>Manufacturer</th>
                                <th>Current Stock</th>
                                <th>Min Level</th>
                                <th>Purchase Price</th>
                                <th>Selling Price</th>
                                <th>Margin %</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="medicines-tbody">
                            <!-- Medicine data will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </main>

    <!-- Add/Edit Medicine Modal -->
    <div id="medicine-modal" class="modal-overlay" style="display: none;">
        <div class="modal">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title">Add New Medicine</h3>
                <button class="modal-close" onclick="closeMedicineModal()">&times;</button>
            </div>
            <div class="modal-body">
                <form id="medicine-form">
                    <input type="hidden" id="medicine-id" name="medicine_id">
                    
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Medicine Name *</label>
                                <input type="text" id="medicine-name" name="medicine_name" class="form-input" required>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Generic Name</label>
                                <input type="text" id="generic-name" name="generic_name" class="form-input">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Category *</label>
                                <select id="category" name="category" class="form-select" required>
                                    <option value="">Select Category</option>
                                    <option value="Pain Relief">Pain Relief</option>
                                    <option value="Antibiotic">Antibiotic</option>
                                    <option value="Cold & Flu">Cold & Flu</option>
                                    <option value="Diabetes">Diabetes</option>
                                    <option value="Vitamins">Vitamins</option>
                                    <option value="Heart">Heart</option>
                                    <option value="Blood Pressure">Blood Pressure</option>
                                    <option value="Digestive">Digestive</option>
                                    <option value="Skin Care">Skin Care</option>
                                    <option value="Others">Others</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label class="form-label">Manufacturer *</label>
                                <input type="text" id="manufacturer" name="manufacturer" class="form-input" required>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Pack Size</label>
                                <input type="text" id="pack-size" name="pack_size" class="form-input">
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Unit Type *</label>
                                <select id="unit-type" name="unit_type" class="form-select" required>
                                    <option value="">Select Unit</option>
                                    <option value="Tablets">Tablets</option>
                                    <option value="Capsules">Capsules</option>
                                    <option value="Bottle">Bottle</option>
                                    <option value="Vial">Vial</option>
                                    <option value="Tube">Tube</option>
                                    <option value="Box">Box</option>
                                    <option value="Strip">Strip</option>
                                    <option value="Injection">Injection</option>
                                    <option value="Syrup">Syrup</option>
                                    <option value="Drops">Drops</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Storage Location</label>
                                <input type="text" id="storage-location" name="storage_location" class="form-input" placeholder="e.g., A-1">
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Purchase Price (Rs.) *</label>
                                <input type="number" id="purchase-price" name="purchase_price" class="form-input" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Selling Price (Rs.) *</label>
                                <input type="number" id="selling-price" name="selling_price" class="form-input" step="0.01" required>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Margin % (Auto-calculated)</label>
                                <input type="text" id="margin-percent" name="margin_percent" class="form-input" readonly>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Current Stock</label>
                                <input type="number" id="current-stock" name="current_stock" class="form-input" value="0">
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Minimum Stock Level *</label>
                                <input type="number" id="min-stock-level" name="min_stock_level" class="form-input" required>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="form-group">
                                <label class="form-label">Prescription Required</label>
                                <select id="prescription-required" name="prescription_required" class="form-select">
                                    <option value="No">No</option>
                                    <option value="Yes">Yes</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">Status</label>
                        <select id="status" name="status" class="form-select">
                            <option value="Active">Active</option>
                            <option value="Inactive">Inactive</option>
                            <option value="Discontinued">Discontinued</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">Save Medicine</button>
                        <button type="button" class="btn btn-danger" onclick="closeMedicineModal()">Cancel</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../js/database.js"></script>
    <script src="../js/medicines.js"></script>
</body>
</html>