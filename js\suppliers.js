// Supplier management functionality

class SupplierManager {
    constructor() {
        this.suppliers = [];
        this.purchases = [];
        this.filteredSuppliers = [];
        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.updateDateTime();
        setInterval(() => this.updateDateTime(), 1000);
    }

    updateDateTime() {
        const now = new Date();
        const dateOptions = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        const timeOptions = { 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        };

        const dateElement = document.getElementById('current-date');
        const timeElement = document.getElementById('current-time');
        
        if (dateElement && timeElement) {
            dateElement.textContent = now.toLocaleDateString('en-US', dateOptions);
            timeElement.textContent = now.toLocaleTimeString('en-US', timeOptions);
        }
    }

    async loadData() {
        try {
            // Wait for database to be ready
            if (!dbManager.db) {
                setTimeout(() => this.loadData(), 1000);
                return;
            }

            this.suppliers = await dbManager.getAll('suppliers');
            this.purchases = await dbManager.getAll('purchases');
            
            this.updateSupplierStats();
            this.filteredSuppliers = [...this.suppliers];
            this.renderSuppliers();
            this.updateStatistics();
        } catch (error) {
            console.error('Error loading supplier data:', error);
        }
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('supplier-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.filterSuppliers(e.target.value));
        }

        // Form submission
        const form = document.getElementById('supplier-form');
        if (form) {
            form.addEventListener('submit', (e) => this.handleFormSubmit(e));
        }
    }

    updateSupplierStats() {
        // Update supplier purchase amounts and outstanding from purchases data
        this.suppliers.forEach(supplier => {
            const supplierPurchases = this.purchases.filter(purchase => purchase.supplier_name === supplier.supplier_name);
            const totalPurchases = supplierPurchases.reduce((sum, purchase) => sum + (parseFloat(purchase.total_amount) || 0), 0);
            const outstandingAmount = supplierPurchases
                .filter(purchase => purchase.payment_status === 'Pending')
                .reduce((sum, purchase) => sum + (parseFloat(purchase.total_amount) || 0), 0);
            const lastPurchase = supplierPurchases.length > 0 ? 
                Math.max(...supplierPurchases.map(purchase => new Date(purchase.date).getTime())) : null;

            supplier.total_purchase_amount = totalPurchases;
            supplier.outstanding_amount = outstandingAmount;
            supplier.last_purchase_date = lastPurchase ? new Date(lastPurchase).toISOString().split('T')[0] : null;
        });
    }

    filterSuppliers(searchTerm) {
        const term = searchTerm.toLowerCase();
        this.filteredSuppliers = this.suppliers.filter(supplier => 
            supplier.supplier_name.toLowerCase().includes(term) ||
            supplier.contact_person.toLowerCase().includes(term) ||
            supplier.phone_number.includes(term) ||
            (supplier.email && supplier.email.toLowerCase().includes(term)) ||
            (supplier.gst_number && supplier.gst_number.toLowerCase().includes(term)) ||
            supplier.supplier_id.toLowerCase().includes(term)
        );
        this.renderSuppliers();
    }

    renderSuppliers() {
        const tbody = document.getElementById('suppliers-tbody');
        if (!tbody) return;

        tbody.innerHTML = '';

        if (this.filteredSuppliers.length === 0) {
            tbody.innerHTML = '<tr><td colspan="13" class="text-center text-muted">No suppliers found</td></tr>';
            return;
        }

        this.filteredSuppliers.forEach(supplier => {
            const row = document.createElement('tr');
            
            // Highlight overdue suppliers
            if (supplier.outstanding_amount > supplier.credit_limit) {
                row.style.backgroundColor = '#ffebee';
            }

            row.innerHTML = `
                <td>${supplier.supplier_id}</td>
                <td>${supplier.supplier_name}</td>
                <td>${supplier.contact_person}</td>
                <td>${supplier.phone_number}</td>
                <td>${supplier.email || '-'}</td>
                <td>${supplier.gst_number || '-'}</td>
                <td>${supplier.payment_terms}</td>
                <td>Rs. ${(supplier.credit_limit || 0).toLocaleString()}</td>
                <td>
                    <span class="${supplier.outstanding_amount > 0 ? (supplier.outstanding_amount > supplier.credit_limit ? 'text-danger' : 'text-warning') : 'text-success'}">
                        Rs. ${(supplier.outstanding_amount || 0).toLocaleString()}
                    </span>
                </td>
                <td>Rs. ${(supplier.total_purchase_amount || 0).toLocaleString()}</td>
                <td>${supplier.last_purchase_date ? new Date(supplier.last_purchase_date).toLocaleDateString() : 'Never'}</td>
                <td><span class="status status-${supplier.status.toLowerCase()}">${supplier.status}</span></td>
                <td>
                    <button class="btn btn-sm btn-primary" onclick="viewSupplierDetails('${supplier.supplier_id}')">View</button>
                    <button class="btn btn-sm btn-warning" onclick="editSupplier('${supplier.supplier_id}')">Edit</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteSupplier('${supplier.supplier_id}')">Delete</button>
                </td>
            `;
            tbody.appendChild(row);
        });
    }

    updateStatistics() {
        const totalSuppliers = this.suppliers.length;
        const activeSuppliers = this.suppliers.filter(s => s.status === 'Active').length;
        const totalPurchases = this.suppliers.reduce((sum, s) => sum + (s.total_purchase_amount || 0), 0);
        const outstandingAmount = this.suppliers.reduce((sum, s) => sum + (s.outstanding_amount || 0), 0);

        document.getElementById('total-suppliers').textContent = totalSuppliers;
        document.getElementById('active-suppliers').textContent = activeSuppliers;
        document.getElementById('total-purchases').textContent = `Rs. ${totalPurchases.toLocaleString()}`;
        document.getElementById('outstanding-amount').textContent = `Rs. ${outstandingAmount.toLocaleString()}`;
    }

    async handleFormSubmit(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const supplierData = Object.fromEntries(formData.entries());
        
        // Convert numeric fields
        supplierData.credit_limit = parseFloat(supplierData.credit_limit) || 0;
        supplierData.outstanding_amount = parseFloat(supplierData.outstanding_amount) || 0;
        supplierData.total_purchase_amount = parseFloat(supplierData.total_purchase_amount) || 0;

        try {
            if (supplierData.supplier_id) {
                // Update existing supplier
                await dbManager.update('suppliers', supplierData);
                this.showAlert('Supplier updated successfully!', 'success');
            } else {
                // Add new supplier
                supplierData.supplier_id = await dbManager.generateId('suppliers', 'SUP');
                await dbManager.insert('suppliers', supplierData);
                this.showAlert('Supplier added successfully!', 'success');
            }
            
            this.closeSupplierModal();
            await this.loadData();
        } catch (error) {
            console.error('Error saving supplier:', error);
            this.showAlert('Error saving supplier. Please try again.', 'danger');
        }
    }

    showAlert(message, type) {
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;
        
        const mainContent = document.querySelector('.main-content');
        mainContent.insertBefore(alert, mainContent.firstChild);

        setTimeout(() => alert.remove(), 3000);
    }

    closeSupplierModal() {
        document.getElementById('supplier-modal').style.display = 'none';
        document.getElementById('supplier-form').reset();
        document.getElementById('supplier-id').value = '';
        document.getElementById('supplier-modal-title').textContent = 'Add New Supplier';
    }

    closeSupplierDetailsModal() {
        document.getElementById('supplier-details-modal').style.display = 'none';
    }
}

// Global functions
function showAddSupplierModal() {
    document.getElementById('supplier-modal').style.display = 'flex';
    document.getElementById('supplier-form').reset();
    document.getElementById('supplier-id').value = '';
    document.getElementById('supplier-modal-title').textContent = 'Add New Supplier';
}

function closeSupplierModal() {
    supplierManager.closeSupplierModal();
}

function closeSupplierDetailsModal() {
    supplierManager.closeSupplierDetailsModal();
}

async function editSupplier(supplierId) {
    try {
        const supplier = await dbManager.get('suppliers', supplierId);
        if (!supplier) {
            alert('Supplier not found');
            return;
        }

        // Populate form with supplier data
        document.getElementById('supplier-id').value = supplier.supplier_id;
        document.getElementById('supplier-name').value = supplier.supplier_name;
        document.getElementById('contact-person').value = supplier.contact_person;
        document.getElementById('phone-number').value = supplier.phone_number;
        document.getElementById('email').value = supplier.email || '';
        document.getElementById('address').value = supplier.address || '';
        document.getElementById('gst-number').value = supplier.gst_number || '';
        document.getElementById('payment-terms').value = supplier.payment_terms;
        document.getElementById('credit-limit').value = supplier.credit_limit || 0;
        document.getElementById('outstanding-amount').value = supplier.outstanding_amount || 0;
        document.getElementById('status').value = supplier.status;

        // Show modal
        document.getElementById('supplier-modal').style.display = 'flex';
        document.getElementById('supplier-modal-title').textContent = 'Edit Supplier';
    } catch (error) {
        console.error('Error loading supplier for edit:', error);
        alert('Error loading supplier data');
    }
}

async function viewSupplierDetails(supplierId) {
    try {
        const supplier = await dbManager.get('suppliers', supplierId);
        if (!supplier) {
            alert('Supplier not found');
            return;
        }

        // Get supplier's purchase history
        const supplierPurchases = supplierManager.purchases.filter(purchase => purchase.supplier_name === supplier.supplier_name);
        
        const detailsContent = `
            <div class="row">
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Contact Information</h4>
                        </div>
                        <div class="card-body">
                            <p><strong>Supplier Name:</strong> ${supplier.supplier_name}</p>
                            <p><strong>Contact Person:</strong> ${supplier.contact_person}</p>
                            <p><strong>Phone:</strong> ${supplier.phone_number}</p>
                            <p><strong>Email:</strong> ${supplier.email || 'Not provided'}</p>
                            <p><strong>Address:</strong> ${supplier.address || 'Not provided'}</p>
                            <p><strong>GST Number:</strong> ${supplier.gst_number || 'Not provided'}</p>
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Business Terms</h4>
                        </div>
                        <div class="card-body">
                            <p><strong>Payment Terms:</strong> ${supplier.payment_terms}</p>
                            <p><strong>Credit Limit:</strong> Rs. ${(supplier.credit_limit || 0).toLocaleString()}</p>
                            <p><strong>Outstanding Amount:</strong> 
                                <span class="${supplier.outstanding_amount > supplier.credit_limit ? 'text-danger' : 'text-success'}">
                                    Rs. ${(supplier.outstanding_amount || 0).toLocaleString()}
                                </span>
                            </p>
                            <p><strong>Status:</strong> <span class="status status-${supplier.status.toLowerCase()}">${supplier.status}</span></p>
                            <p><strong>Available Credit:</strong> Rs. ${Math.max(0, (supplier.credit_limit || 0) - (supplier.outstanding_amount || 0)).toLocaleString()}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">Purchase Summary</h4>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-3">
                                    <div class="stat-card">
                                        <div class="stat-value">Rs. ${(supplier.total_purchase_amount || 0).toLocaleString()}</div>
                                        <div class="stat-label">Total Purchases</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="stat-card">
                                        <div class="stat-value">${supplierPurchases.length}</div>
                                        <div class="stat-label">Total Orders</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="stat-card">
                                        <div class="stat-value">${supplierPurchases.filter(p => p.payment_status === 'Pending').length}</div>
                                        <div class="stat-label">Pending Payments</div>
                                    </div>
                                </div>
                                <div class="col-3">
                                    <div class="stat-card">
                                        <div class="stat-value">${supplier.last_purchase_date ? new Date(supplier.last_purchase_date).toLocaleDateString() : 'Never'}</div>
                                        <div class="stat-label">Last Purchase</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Recent Purchase History</h4>
                </div>
                <div class="card-body">
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Purchase ID</th>
                                    <th>Invoice Number</th>
                                    <th>Medicine</th>
                                    <th>Quantity</th>
                                    <th>Amount</th>
                                    <th>Payment Status</th>
                                    <th>Due Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${supplierPurchases.slice(0, 10).map(purchase => `
                                    <tr>
                                        <td>${new Date(purchase.date).toLocaleDateString()}</td>
                                        <td>${purchase.purchase_id}</td>
                                        <td>${purchase.invoice_number || '-'}</td>
                                        <td>${purchase.medicine_name}</td>
                                        <td>${purchase.quantity_ordered}</td>
                                        <td>Rs. ${parseFloat(purchase.total_amount).toFixed(2)}</td>
                                        <td><span class="status status-${purchase.payment_status.toLowerCase()}">${purchase.payment_status}</span></td>
                                        <td>${purchase.payment_due_date ? new Date(purchase.payment_due_date).toLocaleDateString() : '-'}</td>
                                    </tr>
                                `).join('')}
                                ${supplierPurchases.length === 0 ? '<tr><td colspan="8" class="text-center text-muted">No purchases found</td></tr>' : ''}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('supplier-details-content').innerHTML = detailsContent;
        document.getElementById('supplier-details-modal').style.display = 'flex';
    } catch (error) {
        console.error('Error loading supplier details:', error);
        alert('Error loading supplier details');
    }
}

async function deleteSupplier(supplierId) {
    if (!confirm('Are you sure you want to delete this supplier? This action cannot be undone.')) {
        return;
    }

    try {
        await dbManager.delete('suppliers', supplierId);
        supplierManager.showAlert('Supplier deleted successfully!', 'success');
        await supplierManager.loadData();
    } catch (error) {
        console.error('Error deleting supplier:', error);
        supplierManager.showAlert('Error deleting supplier. Please try again.', 'danger');
    }
}

function exportSuppliers() {
    try {
        const headers = [
            'Supplier ID', 'Supplier Name', 'Contact Person', 'Phone Number', 'Email',
            'Address', 'GST Number', 'Payment Terms', 'Credit Limit',
            'Outstanding Amount', 'Total Purchase Amount', 'Last Purchase Date', 'Status'
        ];
        
        let csvContent = headers.join(',') + '\n';
        
        supplierManager.suppliers.forEach(supplier => {
            const row = [
                supplier.supplier_id,
                `"${supplier.supplier_name}"`,
                `"${supplier.contact_person}"`,
                supplier.phone_number,
                `"${supplier.email || ''}"`,
                `"${supplier.address || ''}"`,
                supplier.gst_number || '',
                supplier.payment_terms,
                supplier.credit_limit || 0,
                supplier.outstanding_amount || 0,
                supplier.total_purchase_amount || 0,
                supplier.last_purchase_date || '',
                supplier.status
            ];
            csvContent += row.join(',') + '\n';
        });
        
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `suppliers_export_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        supplierManager.showAlert('Suppliers exported successfully!', 'success');
    } catch (error) {
        console.error('Error exporting suppliers:', error);
        supplierManager.showAlert('Error exporting suppliers. Please try again.', 'danger');
    }
}

// Initialize supplier manager when page loads
let supplierManager;
document.addEventListener('DOMContentLoaded', () => {
    supplierManager = new SupplierManager();
});